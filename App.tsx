import React, { useState, useEffect, useRef } from "react";
import { StatusBar } from "expo-status-bar";
import {
  View,
  Platform,
  AppState,
  AppStateStatus,
  Alert,
  Linking,
} from "react-native";
import { enableScreens } from "react-native-screens";
import * as SplashScreen1 from "expo-splash-screen";
import { useFonts } from "expo-font";
import MainStack from "./app/navigation/MainStack";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "./app/RequestHandlers/CredentailsContext";
import i18n, { initLanguage } from "./i18n"; // Import your i18n instance
import { LanguageProvider } from "./app/context/LanguageContext";
import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from "@react-native-google-signin/google-signin";
import { SmileID } from "@smile_identity/react-native";
import { FingerPrintStatus } from "./app/context/FingerPrintContext";
import { DepositProvider } from "./app/context/DepositeContext";
import * as Device from "expo-device";
import * as Notifications from "expo-notifications";
import Constants from "expo-constants";
import { AddPushToken, CheckSession } from "./app/RequestHandlers/User";
import { UpdateUser } from "./app/RequestHandlers/User";
import { navigate } from "./app/navigation/navigationRef";
import CustomSplashScreen from "./app/components/CustomSplashScreen";
import OffLineToast from "./app/components/ErrorSate/OfflineComponent";
import { clearAllUserCache } from "./app/Utils/userDetailsCacheUtils";
import { UpdateModal } from "./app/screens/UpdateModal";
import DeviceInfo from "react-native-device-info";
import { DeviceIdContext } from "./app/RequestHandlers/DeviceIdContext";
import { ToastProvider } from "./app/context/ToastContext";
import * as SecureStore from "expo-secure-store";
import * as Updates from "expo-updates";
import * as Application from "expo-application";
import { GlobalModalProvider } from "./app/context/GlobalModalContext";
import { WelcomeModalProvider } from "./app/context/WelcomeModalContext";

SplashScreen1.setOptions({
  duration: 1000,
  fade: true,
});
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

function handleRegistrationError(errorMessage: string) {
  alert(errorMessage);
  throw new Error(errorMessage);
}
async function AddPushNotificationToken(body) {
  try {
    const res = await AddPushToken(body);
  } catch (error) {}
}
async function registerForPushNotificationsAsync() {
  if (Platform.OS === "android") {
    Notifications.setNotificationChannelAsync("default", {
      name: "default",
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: "#FF231F7C",
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } =
      await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    if (existingStatus !== "granted") {
      const { status } = await Notifications.requestPermissionsAsync();
      if (status === "granted") {
        try {
          const res = await UpdateUser({ pushnotification: true });
        } catch (error) {}
      }
      finalStatus = status;
    }
    if (finalStatus !== "granted") {
      handleRegistrationError(
        "Permission not granted to get push token for push notification!"
      );
      return;
    }
    const projectId =
      Constants?.expoConfig?.extra?.eas?.projectId ??
      Constants?.easConfig?.projectId;
    if (!projectId) {
      handleRegistrationError("Project ID not found");
    }
    try {
      const pushToken = await Notifications.getExpoPushTokenAsync({
        projectId,
      });
      return pushToken.data;
    } catch (e: unknown) {
      // handleRegistrationError(`${e}`);
    }
  } else {
    // handleRegistrationError('Must use physical device for push notifications');
  }
}

// Silent EAS Update Function
async function checkForEASUpdate() {
  try {
    const update = await Updates.checkForUpdateAsync();
    if (update.isAvailable) {
      await Updates.fetchUpdateAsync();
      await Updates.reloadAsync(); // Restart to apply the update silently
    }
  } catch (error) {
    console.log("EAS Update Error:", error);
  }
}

export default function App() {
  const [userId, setUserId] = useState(null);
  const appState = useRef(AppState.currentState);
  const [apppState, setAppState] = useState(appState.current);
  const lastActiveTime = useRef<number>(new Date().getTime());
  const hasNavigatedToLogin = useRef(false);
  useEffect(() => {
    SmileID.initialize(false);
    // SmileID.setCallbackUrl("https://dev-api.sfxchange.app/");
    // SmileID.setCallbackUrl("https://dev-api.sfxchange.app/kyc/callback");
  }, []);
  GoogleSignin.configure({
    webClientId:
      "532609421408-3m8b2bfi1gmah9nuebe02mtpasa1fc5v.apps.googleusercontent.com",
    iosClientId:
      "532609421408-buqsghmlhuph82b65sipjs745p9mc1h8.apps.googleusercontent.com",
    profileImageSize: 120,
  });
  enableScreens();
  const [appReady, setAppReady] = useState(false);
  const [storedCredentails, setStoredCredentails] = useState("");
  const [storedFingerPrintStatus, setStoredFingerPrintStatus] = useState("");
  const [storedPrivateKey, setStoredPrivateKey] = useState("");
  const [storedID, setStoredID] = useState("");
  const [storedHasPinStatus, setStoredHasPinStatus] = useState("");
  const [fontsLoaded] = useFonts({
    "poppins-bold": require("./app/assets/fonts/Poppins-Bold.ttf"),
    "poppins-semibold": require("./app/assets/fonts/Poppins-SemiBold.ttf"),
    "poppins-medium": require("./app/assets/fonts/Poppins-Medium.ttf"),
    "poppins-regular": require("./app/assets/fonts/Poppins-Regular.ttf"),
  });
  const [notification, setNotification] = useState<
    Notifications.Notification | undefined
  >(undefined);
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    checkForEASUpdate();
  }, []);

  useEffect(() => {
    const fetchUniqueId = async () => {
      const id = await DeviceInfo.getUniqueId();
      setStoredID(id);
      AsyncStorage.setItem("uniqueID", id);
    };

    fetchUniqueId();
    _registerForPushNotifications();
    notificationListener.current =
      Notifications.addNotificationReceivedListener((notification) => {
        setNotification(notification);
      });
    responseListener.current =
      Notifications.addNotificationResponseReceivedListener((response) => {});
    return () => {
      notificationListener.current &&
        Notifications.removeNotificationSubscription(
          notificationListener.current
        );
      responseListener.current &&
        Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, []);
  useEffect(() => {
    if (appReady) {
      SplashScreen1.hideAsync();
    }
  }, [appReady]);

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen1.hideAsync();
    }
  }, [fontsLoaded]);
  async function _registerForPushNotifications() {
    try {
      const _pushToken = await AsyncStorage.getItem("pusk_token");
      if (_pushToken) return;
      const pushToken = await registerForPushNotificationsAsync();
      AsyncStorage.setItem("push_token", pushToken);
      AddPushNotificationToken({
        token: pushToken,
        deviceType: Platform.OS === "ios" ? "ios" : "android",
      });
    } catch (error) {}
  }
  const clearLogin = async () => {
    // Clear all cache before logging out
    await clearAllUserCache();

    await GoogleSignin.signOut();
    AsyncStorage.removeItem("login2fa")
      .then(() => {})
      .catch((error) => {});
    AsyncStorage.removeItem("cookies")
      .then(() => {
        // @ts-ignore
        setStoredCredentails(null);
      })
      .catch((error) => {});
  };
  // check token expiration
  const parseJwt = (token) => {
    if (token) {
      const tkn = token.token;
      const payload = tkn.split(".")[1];
      const decodedPayload = JSON.parse(atob(payload));
      if (decodedPayload.exp * 1000 < new Date().getTime()) {
        clearLogin();
      }
    }
  };
  let appStateSubscription;
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    // Skip if user does not have a pin
    // @ts-ignore
    if (!storedCredentails?.user?.hasPin) {
      return;
    }

    // Track state transitions
    const prevState = appState.current;
    const isEnteringBackground = nextAppState === "background";
    const isEnteringForeground = nextAppState === "active";
    const isIOSInactive = nextAppState === "inactive" && Platform.OS === "ios";

    // Handle iOS transition sequence: active → inactive → background
    if (isIOSInactive && prevState === "active") {
      console.log("back3");
      // Treat as early background transition start
      lastActiveTime.current = Date.now();
      hasNavigatedToLogin.current = false;
    }

    // When app returns to active state
    if (isEnteringForeground) {
      console.log("back1");
      const backgroundEntryTime = lastActiveTime.current;
      const currentTime = Date.now();

      if (backgroundEntryTime > 0) {
        const inactiveDuration = (currentTime - backgroundEntryTime) / 1000;

        // iOS may report "inactive" instead of "background"
        const wasInBackground =
          prevState === "background" ||
          (Platform.OS === "ios" && prevState === "inactive");

        if (
          wasInBackground &&
          inactiveDuration > 300 &&
          !hasNavigatedToLogin.current
        ) {
          hasNavigatedToLogin.current = true;
          setTimeout(() => {
            navigate("ExistingLoginScreenReturn");
          }, 100);
        }
      }
    }

    // When app fully enters background
    if (isEnteringBackground) {
      console.log("back");

      lastActiveTime.current = Date.now();
      hasNavigatedToLogin.current = false;
    }

    // Update state tracking
    appState.current = nextAppState;
  };

  useEffect(() => {
    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );
    return () => subscription.remove();
  }, [storedCredentails]);

  const checkSession = async () => {
    try {
      const res = await CheckSession();
      // console.log(res);
      if (res.status == false || res.error) {
        clearLogin();
      }
    } catch (error) {}
  };

  // checkLoginCredentails function has been incorporated into initializeApp

  const checkFingerPrintStatus = async (userId: string) => {
    try {
      // Check if secure storage is available
      await SecureStore.isAvailableAsync();

      // Get fingerprint status and private key
      const res = await AsyncStorage.getItem(`fingerPrintStatus${userId}`);
      const privateKey = await SecureStore.getItemAsync(`privateKey${userId}`);

      // Update fingerprint status
      if (res !== null) {
        setStoredFingerPrintStatus(res);
      } else {
        setStoredFingerPrintStatus(null);
      }

      // Update private key
      if (privateKey !== null) {
        setStoredPrivateKey(privateKey);
      } else {
        setStoredPrivateKey(null);
      }
    } catch (error) {
      console.error("Error checking fingerprint status:", error);
    }
  };
  useEffect(() => {
    // clearLogin()
    const interval = setInterval(() => {
      if (storedCredentails != null) {
        parseJwt(storedCredentails);
        checkSession();
      }
    }, 60000);
    return () => clearInterval(interval);
  }, [storedCredentails]);

  const initializeApp = async () => {
    try {
      console.log("Initializing language settings");
      await initLanguage(); // Initialize language settings
      // Convert checkLoginCredentails to a promise
      return new Promise<void>((resolve) => {
        console.log("Checking login credentials");
        // Get login credentials
        setTimeout(async () => {
          try {
            const res = await AsyncStorage.getItem("cookies");
            if (res != null && typeof res === "string") {
              const newRes = JSON.parse(res);
              setStoredCredentails(newRes);
              parseJwt(newRes);
              checkFingerPrintStatus(newRes.user.id);
              setUserId(newRes.user.id);
              checkSession();
            } else {
              setStoredCredentails(null);
            }
            setAppReady(true);
            console.log("Login credentials checked");
            resolve(); // Resolve the promise when done
          } catch (err) {
            console.error("Error checking login credentials:", err);
            setAppReady(true);
            resolve(); // Resolve even on error
          }
        }, 1000); // Reduced timeout to improve performance
      });
    } catch (error) {
      console.error("Error in app initialization:", error);
      setAppReady(true);
    }
  };
  // useEffect(() => {
  //  customFunc()
  // }, []);

  const [isSplashComplete, setIsSplashComplete] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  // Handle splash screen completion
  const handleSplashComplete = () => {
    console.log("Splash animation complete");
    setIsSplashComplete(true);
  };

  // Initialize app only once
  useEffect(() => {
    const initialize = async () => {
      if (isInitializing) {
        console.log("Starting app initialization");
        await initializeApp();
        setIsInitializing(false);
        console.log("App initialization complete");
      }
    };

    initialize();
  }, [isInitializing]);

  // Show splash screen during initialization or while fonts are loading
  if (isInitializing || !appReady || !fontsLoaded || !isSplashComplete) {
    return (
      <CustomSplashScreen
        onFinish={handleSplashComplete}
        appIsReady={!isInitializing && appReady && fontsLoaded}
      />
    );
  }

  return (
    <>
      <StatusBar style="auto" translucent={true} />
      <LanguageProvider>
        <CredentailsContext.Provider
          // @ts-ignore
          value={{ storedCredentails, setStoredCredentails }}
        >
          <FingerPrintStatus.Provider
            value={{
              storedFingerPrintStatus,
              //@ts-ignore
              setStoredFingerPrintStatus,
              storedPrivateKey,
              // @ts-ignore
              setStoredPrivateKey,
            }}
          >
            {/* @ts-ignore */}
            <DeviceIdContext.Provider value={{ storedID, setStoredID }}>
              <WelcomeModalProvider>
                <DepositProvider>
                  <ToastProvider>
                    <GestureHandlerRootView style={{ flex: 1 }}>
                      <OffLineToast />
                      <UpdateModal />
                      <GlobalModalProvider>
                        <MainStack />
                      </GlobalModalProvider>
                    </GestureHandlerRootView>
                  </ToastProvider>
                </DepositProvider>
              </WelcomeModalProvider>
            </DeviceIdContext.Provider>
          </FingerPrintStatus.Provider>
        </CredentailsContext.Provider>
      </LanguageProvider>

      {/* Custom Splash Screen */}
    </>
  );
}

// Removed unused styles
