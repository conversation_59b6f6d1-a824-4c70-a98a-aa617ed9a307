import React, { useEffect, useState } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  Dimensions,
  TextInput,
} from "react-native";
import { colors } from "../../../config/colors";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import Div from "../../../components/Div";
import NoteComponent2 from "../../../components/NoteComponent2";
import Input from "../../../components/Input";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import BottomSheet from "../../../components/BottomSheet";
import P from "../../../components/P";
import { fonts } from "../../../config/Fonts";
import { GetLinkBanks, GetLinkVendors } from "../../../RequestHandlers/Wallet";
import Loader from "../../../components/ActivityIndicator";
import Button from "../../../components/Button";
import { validate } from "uuid";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";
import { useToast } from "../../../context/ToastContext";

const { width, height } = Dimensions.get("window");
function LinkAddAccountSelect({ navigation, route }) {
  const [showVendor, setShowVendors] = useState(false);
  const [selectedVendors, setSelectedVendors] = useState(null);
  const [vendors, setVendors] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [accNumber, setAccNumber] = useState("");
  const [banks, setBanks] = useState<any>([]);
  const [showBanks, setShowBanks] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedBanks, setSelectedBanks] = useState(null);
  const [accErr, setAccError] = useState("");
  const [bankErr, setBankError] = useState("");
  const { data } = route.params || {};
  const [isOn, setIsOn] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState<any>([]);
  const { handleToast } = useToast();

  const filteredBank = searchQuery
    ? banks.filter((bank) =>
        bank.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : banks;
  const getVendors = async () => {
    setLoading(true);
    try {
      const res1 = await withApiErrorToast(GetLinkVendors(), handleToast);
      if (res1[0].Vendors) {
        setVendors(res1[0].Vendors);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getbanks = async () => {
    try {
      const res = await GetLinkBanks();
      if (res[0].Banks) {
        setBanks(res[0].Banks);
      }
    } catch (error) {}
  };
  useEffect(() => {
    getVendors();
    getbanks();
  }, []);

  const validateInputs = () => {
    let errorHandling = 1;
    if (selectedVendors === null) {
      setError("field is required ");
      errorHandling = 0;
    } else {
      setError("");
      errorHandling = 1;
    }

    if (accNumber.length === 0) {
      setAccError("Account number is required");
      errorHandling = 0;
    } else if (accNumber.length < 10) {
      setAccError("incomplete account number");
      errorHandling = 0;
    } else {
      setAccError("");
      errorHandling = 1;
    }

    if (selectedBanks === null) {
      setBankError("field is required");
      errorHandling = 0;
    } else {
      setBankError("");
      errorHandling = 1;
    }

    if (errorHandling == 1) {
      navigation.navigate("LinkAddConfirmDetail", {
        data,
        data2: {
          selectedVendors: selectedVendors,
          accNumber: accNumber,
          selectedBanks: selectedBanks,
        },
      });
    }
  };
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear navigation={navigation} text="Confirm details" />
        <ScrollView>
          <View style={styles.bodyCont}>
            <NoteComponent2
              text={
                "Select vendor account below. Send payment to vendors account Include reference number."
              }
            />
            <TouchableOpacity
              onPress={() => {
                setShowVendors(true);
              }}
              style={{ marginTop: 24 }}
            >
              <View>
                <Input
                  value={
                    selectedVendors != null ? selectedVendors.venderName : ""
                  }
                  label="Vendor"
                  placeholder="John Doe"
                  inputStyle={{ width: "65%", color: "#161817" }}
                  // contStyle={{ marginTop: 16 }}
                  editable={false}
                  leftIcon={
                    <Image
                      source={require("../../../assets/vendor.png")}
                      style={{
                        width: 24,
                        height: 24,
                        marginLeft: 14,
                        objectFit: "contain",
                      }}
                    />
                  }
                  rightIcon={
                    <View
                      style={{
                        //   backgroundColor: "red",
                        width: "15%",
                        height: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <SvgXml xml={svg.dropDown} />
                    </View>
                  }
                  error={error != ""}
                />
                {error != "" && <P style={styles.errorText}>{error}</P>}
              </View>
            </TouchableOpacity>

            <View style={styles.line}></View>
            <NoteComponent2
              text={
                "Please input the senders or your-account number and bank name so as to help us track your transfer easily"
              }
            />
            <View style={{ marginTop: 24 }}>
              <Input
                value={accNumber}
                label="Account Number"
                placeholder="Enter your account number"
                keyboardType="numeric" // Ensures a numeric keyboard for account numbers
                onChangeText={(text) => {
                  setAccNumber(text);
                  setAccError("");
                }}
                error={accErr != ""}
              />
              {accErr != "" && <P style={styles.errorText}>{accErr}</P>}
            </View>
            <TouchableOpacity
              onPress={() => {
                setShowBanks(true);
              }}
              style={{ marginTop: 24 }}
            >
              <View>
                <Input
                  value={selectedBanks != null ? selectedBanks.name : ""}
                  label="Bank name"
                  placeholder="Select bank"
                  inputStyle={{ width: "65%", color: "#161817" }}
                  // contStyle={{ marginTop: 16 }}
                  editable={false}
                  leftIcon={
                    <Image
                      source={{
                        uri:
                          selectedBanks != null
                            ? selectedBanks.image
                            : "https://res.cloudinary.com/dqw0lwkil/image/upload/v1675739938/LINK/Bank_List/accessbank_w23utv.png",
                      }}
                      style={{
                        width: 24,
                        height: 24,
                        marginLeft: 14,
                        objectFit: "cover",
                        borderRadius: 100,
                      }}
                    />
                  }
                  rightIcon={
                    <View
                      style={{
                        //   backgroundColor: "red",
                        width: "15%",
                        height: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <SvgXml xml={svg.dropDown} />
                    </View>
                  }
                  error={bankErr != ""}
                />
                {bankErr != "" && <P style={styles.errorText}>{bankErr}</P>}
              </View>
            </TouchableOpacity>
          </View>
          <Button
            style={{ width: "70%", alignSelf: "center", marginTop: 32 }}
            btnText="Confirm"
            onPress={() => {
              validateInputs();
            }}
          />
          {/* 
          <View style={styles.detailWrap2}>
            <View style={styles.deatilsHead}>
              <P
                style={{
                  color: "#A5A1A1",
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Sender
              </P>

              {beneficiaries.length > 0 && (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  <TouchableOpacity
                    onPress={() => navigation.navigate("Beneficiaries")}
                  >
                    <P
                      style={{
                        color: "#8B52FF",
                        textDecorationLine: "underline",
                        alignItems: "center",
                        fontSize: 12,
                      }}
                    >
                      View all
                    </P>
                  </TouchableOpacity>
                </View>
              )}
            </View>
            {beneficiaries.length === 0 ? (
              <View style={{ alignItems: "center", justifyContent: "center" }}>
                <SvgXml
                  xml={svg.walletFace}
                  style={{ marginTop: 52, marginBottom: 16 }}
                />
                <P
                  style={{
                    fontFamily: fonts.poppinsMedium,
                    marginBottom: 4,
                    fontSize: 12,
                  }}
                >
                  No vendor!
                </P>
                <P
                  style={{
                    color: "#A5A1A1",
                    fontFamily: fonts.poppinsRegular,
                    fontSize: 12,
                  }}
                >
                  You have no saved vendor
                </P>
              </View>
            ) : (
              <>
                {beneficiaries.map((i, index) => (
                  <TouchableOpacity
                    key={index}
                    style={{ width: "100%" }}
                    onPress={() => {}}
                  >
                    <View
                      style={{
                        width: "100%",
                        marginTop: 18,
                        flexDirection: "row",
                        paddingLeft: 24,
                        paddingRight: 24,
                        alignItems: "center",
                      }}
                    >
                      <SvgXml xml={svg.smalllogo} />
                      <View style={{ marginLeft: 16 }}>
                        <P
                          style={{
                            fontSize: 12,
                            // lineHeight: 18,
                            marginBottom: 4,
                          }}
                        >
                          {i?.name}
                        </P>
                        <P
                          style={{
                            fontSize: 12,
                            lineHeight: 18,
                            color: colors.gray,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {i?.account} | SFx money app
                        </P>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </>
            )}
          </View> */}
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={showVendor}
        showBackArrow={false}
        backspaceText="Vendor"
        onClose={() => setShowVendors(false)}
        modalContentStyle={{ height: "70%" }}
        extraModalStyle={{ height: "68%" }}
        components={
          <View style={{ marginTop: 24, width: "100%", alignSelf: "center" }}>
            <P
              style={{
                fontSize: 12,
                fontFamily: fonts.poppinsRegular,
                color: colors.gray,
                marginBottom: 6,
              }}
            >
              Select a vendor you want to send money to below
            </P>
            <ScrollView
              contentContainerStyle={{ paddingBottom: 300 }}
              showsVerticalScrollIndicator={false}
            >
              {vendors.map((item, index) => {
                return (
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      setSelectedVendors(item);
                      setShowVendors(false);
                      setError("");
                    }}
                    style={{
                      width: "100%",
                      paddingLeft: 16,
                      paddingRight: 16,
                      alignItems: "center",
                      flexDirection: "row",
                      backgroundColor:
                        selectedVendors === item
                          ? "rgba(241, 235, 255, 1)"
                          : "transparent",
                      paddingTop: 8,
                      paddingBottom: 8,
                      marginBottom: 16,
                      borderRadius: 8,
                    }}
                  >
                    <Image
                      source={require("../../../assets/vendor.png")}
                      style={{
                        width: 24,
                        height: 24,
                        marginRight: 8,
                        objectFit: "contain",
                      }}
                    />
                    <View>
                      <P
                        style={{
                          fontSize: 12,
                          fontFamily: fonts.poppinsRegular,
                        }}
                      >
                        {item.venderName}
                      </P>
                      <P
                        style={{
                          fontSize: 12,
                          color: colors.gray,
                          fontFamily: fonts.poppinsRegular,
                          marginTop: 4,
                        }}
                      >
                        {item.vendorBank} ~ {item.vendorNumber}
                      </P>
                    </View>
                  </TouchableOpacity>
                );
              })}
              <NoteComponent2
                text={
                  "Please only send money to the vendor you have selected from the above list."
                }
              />
            </ScrollView>
          </View>
        }
      />
      <BottomSheet
        isVisible={showBanks}
        showBackArrow={false}
        backspaceText="Banks"
        onClose={() => setShowBanks(false)}
        modalContentStyle={{ height: "70%" }}
        extraModalStyle={{ height: "68%" }}
        components={
          <View style={{ width: "100%", alignSelf: "center" }}>
            <View style={styles.search}>
              <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search bank"
                cursorColor={colors.black}
                value={searchQuery}
                onChangeText={(text) => setSearchQuery(text)}
              />
            </View>
            <P
              style={{
                marginTop: 16,
                fontSize: 12,
                lineHeight: 19.2,
                color: colors.gray,
              }}
            >
              Select your bank
            </P>
            <ScrollView
              contentContainerStyle={{ paddingBottom: 400 }}
              showsVerticalScrollIndicator={false}
            >
              {filteredBank.map((item, index) => {
                return (
                  <TouchableOpacity
                    key={index}
                    onPress={() => {
                      setSelectedBanks(item);
                      setShowBanks(false);
                      // setAccNumber("")
                      setBankError("");
                    }}
                    style={{
                      width: "100%",
                      paddingLeft: 16,
                      paddingRight: 16,
                      alignItems: "center",
                      flexDirection: "row",
                      backgroundColor:
                        selectedBanks === item
                          ? colors.lowOpPrimary2
                          : "transparent",
                      paddingTop: 8,
                      paddingBottom: 8,
                      marginBottom: 16,
                      borderRadius: 8,
                    }}
                  >
                    <Image
                      source={{ uri: item.image }}
                      style={{
                        width: 24,
                        height: 24,
                        marginRight: 8,
                        objectFit: "cover",
                        borderRadius: 100,
                      }}
                    />
                    <View>
                      <P
                        style={{
                          fontSize: 12,
                          fontFamily: fonts.poppinsRegular,
                        }}
                      >
                        {item.name}
                      </P>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        }
      />
      {loading && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  bodyCont: {
    marginTop: 16,
    width: "90%",
    backgroundColor: colors.white,
    padding: 24,
    alignSelf: "center",
    borderRadius: 12,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  line: {
    marginTop: 24,
    marginBottom: 24,
    width: "100%",
    borderBottomWidth: 1,
    borderStyle: "dashed",
    borderColor: "rgba(230, 229, 229, 1)",
  },
  search: {
    width: "100%",
    height: (5.5 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    marginTop: 24,
    borderWidth: 1,
    borderRadius: 99,
    borderColor: colors.stroke,
    padding: 12,
    paddingLeft: 14,
    paddingRight: 14,
    flexDirection: "row",
  },
  searchInput: {
    width: "90%",
    height: 24,
    fontFamily: fonts.poppinsRegular,
    lineHeight: 24,
    fontSize: 14,
  },
  detailWrap2: {
    // padding: 24,
    width: "90%",
    alignSelf: "center",
    height: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
  },

  desCont: {
    width: "100%",
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
});

export default LinkAddAccountSelect;
