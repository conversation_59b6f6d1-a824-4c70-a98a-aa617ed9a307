import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  StatusBar,
  Modal,
  BackHandler,
  KeyboardAvoidingView,
  Platform,
  Linking,
  Alert,
} from "react-native";
import { colors } from "../../../config/colors";
import P from "../../../components/P";
import { fonts } from "../../../config/Fonts";
import Button from "../../../components/Button";
import Link from "../../../components/Link";
import {
  useNavigation,
  useFocusEffect,
  CommonActions,
} from "@react-navigation/native";
import {
  GetRateByCountry,
  GetTransationById,
} from "../../../RequestHandlers/Wallet";
import Loader from "../../../components/ActivityIndicator";
import Div from "../../../components/Div";
import H4 from "../../../components/H4";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import ViewShot from "react-native-view-shot";
import * as Sharing from "expo-sharing";
import { useToast } from "../../../context/ToastContext";
import {
  formatToFourDecimals,
  formatToTwoDecimals,
} from "../../../Utils/numberFormat";
import DetailCard from "../../../components/DetailCard";
import Dash from "../../../components/Dash";
import { formatDate } from "../../../components/FormatDate";
import AuthenticationHeader from "../../../components/AuthenticationHedear";

interface PProps {
  okayPress?: any;
  viewDetailPress?: any;
  tranStat?: "failed" | "success" | "pending";
  visible?: true | false;
  requestClose?: any;
  from?: string;
}
const { width, height } = Dimensions.get("window");
export default function OTCTransactionStatus({ navigation, route }) {
  const [stImg, setStImg] = useState(
    require("../../../assets/alert-circle.png")
  );
  const [stText, setStText] = useState("Sent money is pending");
  const [tranStat, setTranState] = useState("pending");
  const { response } = route?.params || {};
  const { bankDetails } = route?.params || {};
  const { username, note, amount, tryAmount, wallet } = route?.params || {};
  const [tranDetails, setTranDetails] = useState<any>([]);
  const [yellowCardData, setYellowCardData] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [loading, setLoading] = useState(false);
  const [receiptLoading, setReceiptLoading] = useState(false);
  const viewShotRef = useRef(null);
  const { handleToast } = useToast();
  const [tryRate, setTryRate] = useState(0);

  // const [tranStat, setTranStat] = useState("pending");

  // Start OTC conversation with bank details
  const startOTC = async () => {
    setLoading(true);

    try {
      const phoneNumber = "+************";
      const usdAmount = formatToFourDecimals(Number(tranDetails?.amount) || 0);
      const tryAmountFormatted = formatToTwoDecimals(
        Number(tranDetails?.amount * tryRate) || 0
      );

      let bankDetailsText = "";
      if (bankDetails) {
        bankDetailsText = `\n\nBank Details:\n• Account Name: ${bankDetails.accountName}\n• Bank: ${bankDetails.bankName}\n• ${bankDetails.accountType}: ${bankDetails.accountNumber}`;
      }

      const message = `Hello SFx OTC, \n\nI would like to send $${usdAmount} USD (₺${tryAmountFormatted} TRY) from my sfx account to my Turkish lira account.${bankDetailsText}\n\nThank you!`;

      const encodedMessage = encodeURIComponent(message);
      const url = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;

      Linking.canOpenURL(url)
        .then((supported) => {
          if (supported) {
            return Linking.openURL(url);
          } else {
            handleToast("WhatsApp is not installed on your device", "error");
          }
        })
        .catch((err) => {
          console.error("Error opening WhatsApp:", err);
          handleToast("Could not open WhatsApp", "error");
        });
    } catch (error) {
      console.error("Error starting OTC:", error);
      handleToast("Failed to start OTC", "error");
    } finally {
      setLoading(false);
    }
  };

  // Share receipt image
  const shareReceipt = async () => {
    setReceiptLoading(true);

    try {
      // Capture the hidden receipt as image
      const uri = await viewShotRef.current.capture();

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: "image/jpeg",
          dialogTitle: "Share Transaction Receipt",
          UTI: "public.jpeg",
        });

        handleToast("Receipt shared successfully!", "success");
      } else {
        handleToast("Sharing is not available on this device", "error");
      }
    } catch (error) {
      console.error("Error sharing receipt:", error);
      handleToast("Failed to share receipt", "error");
    } finally {
      setReceiptLoading(false);
    }
  };
  const getRateById = async () => {
    try {
      const sfxRate = await GetRateByCountry("TRY", "tcmb");
      sfxRate.map((item, index) => {
        if (item.type === "sell") {
          setTryRate(item.amount);
        }
      });
    } catch (error) {}
  };
  const getTransaction = async () => {
    try {
      const id =
        response.id === undefined ? response?.transaction?.id : response?.id;
      const transaction = await GetTransationById(id);
      if (transaction) {
        setLoader(false);
      }
      if (transaction.transaction) {
        setTranDetails(transaction.transaction);
      }
      if (transaction.yellowCardData) {
        setYellowCardData(transaction.yellowCardData);
      }
      transaction.transaction.status === "completed"
        ? setTranState("success")
        : transaction.transaction.status === "failed"
        ? setTranState("failed")
        : setTranState("pending");
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    if (tranStat === "failed") {
      setStImg(require("../../../assets/cancel-circle.png"));
      setStText("Sent money failed");
    } else if (tranStat === "success") {
      setStImg(require("../../../assets/success.png"));
      setStText("Money successfully sent");
    } else {
      setStImg(require("../../../assets/alert-circle.png"));
    }
  }, [tranStat]);

  useEffect(() => {
    setLoader(true);
    getRateById();
    if (response) {
      getTransaction();
    }
  }, []);

  // Separate useEffect for polling that depends on transaction status
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    // Only start polling if transaction status is pending
    if (tranStat === "pending") {
      interval = setInterval(() => {
        getTransaction();
      }, 1000);
    }

    // Cleanup function to clear interval
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [tranStat]); // Re-run when tranStat changes

  // Handle back navigation
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        // Reset navigation stack to avoid loading issues
        // @ts-ignore
        // navigation.navigate("BottomTabNavigator");
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "BottomTabNavigator" }],
          })
        );
        return true;
      };
      // Disable iOS swipe back gesture
      navigation.setOptions({
        gestureEnabled: false,
      });
      // Handle Android back button
      BackHandler.addEventListener("hardwareBackPress", onBackPress);

      return () => {
        BackHandler.removeEventListener("hardwareBackPress", onBackPress);
      };
    }, [navigation])
  );

  if (tranStat === "success") {
    return (
      <View style={{ backgroundColor: colors.white, flex: 1 }}>
        <Div>
          <AuthenticationHeader navigation={navigation} goHome={true} />
          {/* Visible Success Screen */}
          <View
            style={{ paddingHorizontal: 24, marginTop: (5 * height) / 100 }}
          >
            <View style={styles.whatsAppIconContainer}>
              <Image
                source={require("../../../assets/celeb.png")}
                style={{ width: 80, height: 80 }}
              />
            </View>
            <H4 style={styles.whatsAppTitle}>
              Money successfully sent{"\n"}Proceed to SFx OTC deck
            </H4>
            <P style={styles.whatsAppSubtitle}>
              To complete your OTC transaction, please continue on WhatsApp
            </P>
            <View style={styles.checkItemContainer}>
              <SvgXml xml={svg.checkGreen} width={20} height={20} />
              <P style={styles.checkItemText}>
                Share your request with otc on whatsapp{" "}
                <P
                  style={{
                    fontFamily: fonts.poppinsRegular,
                    fontSize: 12,
                    textDecorationLine: "underline",
                  }}
                >
                  +************
                </P>{" "}
                is the only OTC channel
              </P>
            </View>
            <View style={styles.checkItemContainer}>
              <SvgXml xml={svg.checkGreen} width={20} height={20} />
              <P style={styles.checkItemText}>
                Receive USD in your SFx wallet after transacting with OTC
              </P>
            </View>
            <View style={styles.reminderContainer}>
              <SvgXml xml={svg.infoCircleOutLine} width={16} height={16} />
              <View>
                <View style={styles.reminderHeader}>
                  <P style={styles.reminderTitle}>Reminder:</P>
                </View>
                <P style={styles.reminderItem}>• Minimum amount: $1 USD</P>
                <P style={styles.reminderItem}>
                  • Upload your payment receipt for confirmation
                </P>
                <P style={styles.reminderItem}>
                  • Your wallet will be credited after verification
                </P>
              </View>
            </View>
            <View style={{ width: "80%", alignSelf: "center", gap: 12 }}>
              <Button
                btnText="Start OTC"
                style={styles.whatsAppButton}
                onPress={startOTC}
                loading={loading}
              />
              <Link
                onPress={() => {
                  shareReceipt();
                }}
                style={{ textAlign: "center", fontSize: 12 }}
              >
                Share Receipt
              </Link>
            </View>
          </View>

          {/* Hidden Receipt Component for Capture */}
          <View
            style={{
              position: "absolute",
              left: -9999,
              top: -9999,
              opacity: 0,
            }}
          >
            <ViewShot
              ref={viewShotRef}
              options={{ format: "jpg", quality: 0.9 }}
            >
              <View style={styles.recieptWrap}>
                <DetailCard
                  type="reciept"
                  image={
                    <Image
                      source={require("../../../assets/sfx2.png")}
                      style={{
                        width: 66.98,
                        height: 24,
                        objectFit: "contain",
                        marginBottom: 24,
                      }}
                    />
                  }
                  amount={
                    <>
                      <P
                        style={{
                          fontSize: 24,
                          lineHeight: 36,
                          marginRight: 2,
                        }}
                      >
                        {tranDetails?.sfxToSfxCurrency !== "USD" ? "₺" : "$"}
                        {tranDetails?.sfxToSfxCurrency !== "USD"
                          ? formatToTwoDecimals(
                              Number(tranDetails?.localAmount)
                            )
                          : formatToTwoDecimals(
                              Number(tranDetails?.amount) || 0
                            )}
                      </P>
                      <P style={{ marginTop: 5 }}>
                        {tranDetails.sfxToSfxCurrency || "USD"}
                      </P>
                    </>
                  }
                  convertedAmount={
                    <>
                      <P
                        style={{
                          fontSize: 16,
                          lineHeight: 24,
                          marginRight: 2,
                        }}
                      >
                        {tranDetails?.sfxToSfxCurrency !== "USD" ? "$" : "₺"}
                        {tranDetails?.sfxToSfxCurrency !== "USD"
                          ? formatToFourDecimals(Number(tranDetails?.amount))
                          : formatToTwoDecimals(
                              Number(tranDetails?.amount * tryRate) || 0
                            )}
                      </P>
                      <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                        {tranDetails.sfxToSfxCurrency !== "USD" ? "USD" : "TRY"}
                      </P>
                    </>
                  }
                  bottomComponent={
                    <View style={styles.desCont}>
                      <View
                        style={{
                          paddingBottom: 24,
                          borderBottomWidth: 1,
                          borderColor: colors.stroke,
                          borderStyle: "dashed",
                        }}
                      >
                        <View style={styles.items}>
                          <P style={styles.holder}>Recipient</P>
                          <View
                            style={{
                              justifyContent: "flex-end",
                              width: "70%",
                            }}
                          >
                            <P style={styles.value}>
                              {tranDetails?.internalTransferReceiver?.firstName}
                              {tranDetails?.internalTransferReceiver?.middleName
                                ? ` ${tranDetails?.internalTransferReceiver?.middleName}`
                                : ""}{" "}
                              {tranDetails?.internalTransferReceiver?.lastName}
                            </P>
                            <P
                              // @ts-ignore
                              style={[
                                styles.value,
                                {
                                  width: "100%",
                                  color: colors.dark500,
                                  marginTop: 4,
                                  fontFamily: fonts.poppinsRegular,
                                },
                              ]}
                            >
                              SFx money app |{" "}
                              {tranDetails?.internalTransferReceiver?.username}
                            </P>
                          </View>
                        </View>
                        <View style={styles.items}>
                          <P style={styles.holder}>Sender</P>
                          <View
                            style={{
                              justifyContent: "flex-end",
                              width: "60%",
                            }}
                          >
                            <P style={styles.value}>
                              {" "}
                              {tranDetails?.user?.firstName}
                              {tranDetails?.user?.middleName
                                ? ` ${tranDetails?.user?.middleName}`
                                : ""}{" "}
                              {tranDetails?.user?.lastName}
                            </P>

                            <P
                              // @ts-ignore
                              style={[
                                styles.value,
                                {
                                  width: "100%",
                                  color: colors.dark500,
                                  marginTop: 4,
                                  fontFamily: fonts.poppinsRegular,
                                },
                              ]}
                            >
                              SFx money app | {tranDetails?.user?.username}
                            </P>
                          </View>
                        </View>
                      </View>
                      {Platform.OS === "ios" && <Dash />}
                      <View style={{ paddingTop: 24 }}>
                        <View style={styles.items}>
                          <P style={styles.holder}>Reference number</P>
                          <View
                            style={{
                              flexDirection: "row",
                              width: 150,
                              justifyContent: "flex-end",
                            }}
                          >
                            <P
                              // @ts-ignore
                              style={[
                                styles.value,
                                { textAlign: "right", width: 120 },
                              ]}
                            >
                              {tranDetails?.ref}
                            </P>
                          </View>
                        </View>
                        <View style={styles.items}>
                          <P style={styles.holder}>Note</P>
                          <View
                            style={{
                              flexDirection: "row",
                              width: 150,
                              justifyContent: "flex-end",
                            }}
                          >
                            <P
                              // @ts-ignore
                              style={[
                                styles.value,
                                { textAlign: "right", width: 120 },
                              ]}
                            >
                              {tranDetails?.reason
                                ? tranDetails?.reason
                                : "None"}
                            </P>
                          </View>
                        </View>
                        <View style={styles.items}>
                          <P style={styles.holder}>Payment method</P>
                          <P style={styles.value}>Send money</P>
                        </View>
                        <View style={[styles.items]}>
                          <P style={styles.holder}>Type</P>
                          <P style={styles.value}>SFx money app</P>
                        </View>
                        <View style={styles.items}>
                          <P style={styles.holder}>Timestamp</P>
                          <P style={styles.value}>
                            {formatDate(tranDetails?.updatedAt)}
                          </P>
                        </View>
                      </View>
                    </View>
                  }
                />
              </View>
            </ViewShot>
          </View>
        </Div>
        {loading && <Loader />}
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.body}
    >
      <View style={styles.itemBox}>
        <Image source={stImg} style={{ width: 64, height: 64 }} />
        <P style={styles.statusState}>{stText}</P>
        {tranStat === "failed" ? (
          <P style={styles.stTx}>
            Money sent failed due to technical issue,{"\n"}please try again
            later!
          </P>
        ) : tranStat === "success" ? (
          <P style={styles.stTx}>
            You have successfully sent {tranDetails?.amount} USD to{"\n"}
            {tranDetails?.internalTransferReceiver
              ? `${tranDetails?.internalTransferReceiver?.firstName} ${tranDetails?.internalTransferReceiver?.lastName}`
              : yellowCardData?.destination?.accountName
              ? yellowCardData?.destination?.accountName
              : tranDetails?.toWallet}
          </P>
        ) : (
          <P style={styles.stTx}>
            Money is processing, please check {"\n"}money status later!
          </P>
        )}

        <View style={{ width: "75%", marginTop: 32 }}>
          <Button
            btnText="Okay!"
            onPress={() => {
              setLoader(false);
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [{ name: "BottomTabNavigator" }],
                })
              );
            }}
          />
          <Link
            style={{ textAlign: "center", marginTop: 16, fontSize: 12 }}
            onPress={() => {
         
              const transactionType =
                tranDetails?.type === "internal-tranfer"
                  ? "sfx money app"
                  : tranDetails?.paymentGayway === "momo"
                  ? "mobile money"
                  : tranDetails?.paymentGayway === "bank"
                  ? "bank transfer"
                  : tranDetails?.provider === "circle"
                  ? "p2p"
                  : "unknown";
              const id =
                response?.id === undefined
                  ? response?.transaction?.id
                  : response?.id;
              navigation.navigate("AllTransactionDetails", {
                id: id,
                transactionType: transactionType,
              });
            }}
          >
            View details
          </Link>
        </View>
      </View>
      {loader && <Loader />}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: (105 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    // top: 0,
    zIndex: 100,
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    // marginTop: (20*height)/100
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  whatsAppModalContent: {
    paddingTop: 24,
    paddingBottom: 24,
  },
  whatsAppIconContainer: {
    alignItems: "center",
    marginBottom: 16,
  },
  whatsAppTitle: {
    fontSize: 18,
    fontFamily: fonts.poppinsBold,
    textAlign: "center",
    marginBottom: 8,
  },
  whatsAppSubtitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
    color: colors.gray,
    marginBottom: 24,
  },
  checkItemContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  checkItemText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginLeft: 8,
    flex: 1,
  },
  reminderContainer: {
    backgroundColor: colors.redSubtle,
    borderLeftWidth: 4,
    borderColor: colors.red,
    padding: 16,
    borderRadius: 8,
    marginVertical: 16,
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  reminderHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  reminderTitle: {
    fontSize: 10,
    fontFamily: fonts.poppinsMedium,
    // marginLeft: 8,
  },
  reminderItem: {
    fontSize: 10,
    fontFamily: fonts.poppinsRegular,
    // marginBottom: 4,
  },
  whatsAppButton: {
    marginTop: 16,
  },
  topSection: {
    width: "100%",
    flex: 1,
    justifyContent: "flex-start",
  },
  bottomSection: {
    width: "100%",
    flex: 1,
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.02),
  },
  recieptWrap: {
    backgroundColor: colors.secBackground,
    paddingTop: 16,
    paddingBottom: 16,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
    textAlign: "right",
    width: "60%",
    alignSelf: "flex-end",
  },
  desCont: {
    width: "100%",
  },
});
