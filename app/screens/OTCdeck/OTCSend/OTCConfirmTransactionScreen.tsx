import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  Linking,
} from "react-native";
import { fonts } from "../../../config/Fonts";
import Div from "../../../components/Div";
import AuthenticationHedear from "../../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";
import P from "../../../components/P";
import { colors } from "../../../config/colors";
import DetailCard from "../../../components/DetailCard";
import Button from "../../../components/Button";
import { GetUserWallet } from "../../../RequestHandlers/Wallet";
import Loader from "../../../components/ActivityIndicator";
import { useToast } from "../../../context/ToastContext";
import {
  formatToFourDecimals,
  formatToTwoDecimals,
} from "../../../Utils/numberFormat";
import BottomSheet from "../../../components/BottomSheet";
import H4 from "../../../components/H4";
import { withApiErrorToast } from "../../../Utils/withApiErrorToast";

const { width, height } = Dimensions.get("window");

export default function OTCConfirmTransactionScreen({ navigation, route }) {
  const {
    username,
    note,
    amount,
    bankDetails,
    tryAmount,
    tryRate,
    currency,
    wallbal,
    walletId,
  } = route.params;
  const { details } = route?.params || {};
  const [loadrer, setLoader] = useState(false);
  const [showWhatsAppModal, setShowWhatsAppModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedAcc, setSelectedAcc] = useState(null);
  const { handleToast } = useToast();
  const [pageload, setpageLoad] = useState(false);
  const [accounts, setAccounts] = useState([
    {
      id: null,
      balance: 0,
      currency: "USDC",
      exchangeRate: "1 USDC ~ 1 USD",
      type: "USDC",
    },
  ]);

  const getUserWallet = async () => {
    setLoader(true);
    try {
      const userWallet = await withApiErrorToast(GetUserWallet(), handleToast);
    } catch (error) {
    } finally {
      setLoader(false);
    }
  };

  // Open WhatsApp with OTC request
  const openWhatsApp = async () => {
    setLoading(true);

    const phoneNumber = "+************";
    const usdAmount = formatToTwoDecimals(Number(amount) || 0);
    const tryAmountFormatted = tryAmount
      ? formatToTwoDecimals(Number(tryAmount) || 0)
      : "0.00";

    // Format bank details for the message
    let bankDetailsText = "";
    if (bankDetails) {
      bankDetailsText = `\n\nBank Details:\n• Account Name: ${bankDetails.accountName}\n• Bank: ${bankDetails.bankName}\n• ${bankDetails.accountType}: ${bankDetails.accountNumber}`;
    } else if (details || username || note) {
      bankDetailsText = `\n\nBank Details:\n• Account Name: ${
        username || "N/A"
      }\n• Bank: ${
        details?.firstName ? `${details.firstName} ${details.lastName}` : "N/A"
      }\n• IBAN: ${note || "N/A"}`;
    }

    const messageString = `Hello SFx OTC, \n\nI would like to send $${usdAmount} USD (₺${tryAmountFormatted} TRY) from my sfx account to my Turkish lira account.${bankDetailsText}\n\nThank you!`;

    const message = encodeURIComponent(messageString);

    // Use the same approach as the working OTC add flow
    const url = `https://wa.me/${phoneNumber}?text=${message}`;

    try {
      // Check if we can open the URL
      const canOpen = await Linking.canOpenURL(url);

      if (canOpen) {
        await Linking.openURL(url);
      } else {
        handleToast("WhatsApp is not installed on your device", "error");
      }
    } catch (err) {
      console.error("Error opening WhatsApp:", err);
      handleToast("Could not open WhatsApp", "error");
    } finally {
      setLoading(false);
      setShowWhatsAppModal(false);
    }
  };

  useEffect(() => {
    if (wallbal && walletId) {
      setAccounts([
        {
          id: walletId,
          balance: wallbal,
          currency: "USDC",
          exchangeRate: "1 USDC ~ 1 USD",
          type: "USDC",
        },
      ]);
    }
  }, [wallbal, walletId]);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Transaction details"
          navigation={navigation}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Money you’re sending"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      {currency === "USD" ? "$" : "₺"}
                      {formatToTwoDecimals(Number(amount) || 0)}
                    </P>
                    <P style={{ marginTop: 5 }}>{currency}</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      {currency === "USD" ? "₺" : "$"}
                      {currency === "USD"
                        ? tryAmount
                          ? formatToTwoDecimals(Number(tryAmount) || 0)
                          : "0.00"
                        : formatToFourDecimals(Number(amount) / tryRate)}
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      {currency == "USD" ? "TRY" : "USD"}
                    </P>
                  </>
                }
                bottomComponent={
                  <View style={styles.desCont}>
                    {bankDetails ? (
                      <>
                        <View style={styles.items}>
                          <P style={styles.holder}>Name</P>
                          <P style={styles.value}>{bankDetails.accountName}</P>
                        </View>
                        <View style={styles.items}>
                          <P style={styles.holder}>Bank</P>
                          <P style={styles.value}>{bankDetails.bankName}</P>
                        </View>
                        <View style={styles.items}>
                          <P style={styles.holder}>{bankDetails.accountType}</P>
                          <P style={[styles.value, { textAlign: "right" }]}>
                            {bankDetails.accountNumber}
                          </P>
                        </View>
                      </>
                    ) : (
                      <>
                        <View style={styles.items}>
                          <P style={styles.holder}>Name</P>
                          <P style={styles.value}>{username}</P>
                        </View>
                        <View style={styles.items}>
                          <P style={styles.holder}>Bank</P>
                          <P style={styles.value}>
                            {details?.firstName} {details?.lastName}
                          </P>
                        </View>
                        <View style={styles.items}>
                          <P style={styles.holder}>IBAN</P>
                          <P style={[styles.value, { textAlign: "right" }]}>
                            {note == "" ? "N/A" : note}
                          </P>
                        </View>
                      </>
                    )}
                    <View style={styles.items}>
                      <P style={styles.holder}>Fee</P>
                      <P style={styles.value}>
                        0{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          USD
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Exchange rate</P>
                      <P style={styles.value}>
                        {tryRate ? `1 USD ~ ${tryRate} TRY` : "1 USD ~ 1 USDC"}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Payment method</P>
                      <P style={styles.value}>Send money</P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Type</P>
                      <P style={styles.value}>OTC deck</P>
                    </View>
                    {/* <View style={[styles.items, { marginBottom: 0 }]}>
                      <P style={styles.holder}>Earn SFx point</P>
                      <P style={styles.value}>$2</P>
                    </View> */}
                    {/* <View style={styles.line}></View>

                    <P
                      style={{
                        color: colors.gray,
                        fontSize: 12,
                        textAlign: "center",
                        marginBottom: 6,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Select payment method
                    </P> */}
                    {/* <View
                      style={{
                        width: "100%",
                        flexDirection: "row",
                        justifyContent: "space-between",
                        marginBottom: 16,
                      }}
                    >
                      <P style={{ color: "#A5A1A1", fontSize: 12 }}>
                        SFx point
                      </P>
                      <View style={{ flexDirection: "row" }}>
                        <P style={{ marginRight: 8 }}>$50</P>
                        <CustomSwitch />
                      </View>
                    </View>
                    {accounts.map((item, index) => (
                      <Content2
                        key={index}
                        svgg={item.currency === "USDT" ? svg.tather : svg.usdCoin}
                        onclick={index === selectedAcc}
                        ClickedMe={() => {
                          if (selectedAcc === index) {
                            setSelectedAcc(null);
                          } else if (item.balance > 0) {
                            setSelectedAcc(index);
                          }
                        }}
                        header={
                          <>
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsMedium,
                              }}
                            >
                              {formatter.format(item.balance)}
                            </P>{" "}
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                fontFamily: fonts.poppinsRegular,
                                color: colors.gray,
                              }}
                            >
                              {item.currency}
                            </P>
                          </>
                        }
                        body={item.exchangeRate}
                        containerStyle={{
                          justifyContent: "flex-start",
                          paddingLeft: 16,
                        }}
                        itemWrapper={{ marginLeft: 8 }}
                        headerStyle={{ marginBottom: 4 }}
                        textStyle={{
                          fontFamily: fonts.poppinsRegular,
                          fontSize: 12,
                        }}
                        rightComponent={
                          item.balance === 0 && (
                            <Button
                              btnText="Add money"
                              btnTextStyle={{ color: colors.primary }}
                              type="alt"
                              style={{ width: "80%", height: "50%" }}
                              onPress={() =>
                                navigation.navigate("AddMoneyScreen")
                              }
                            />
                          )
                        }
                      />
                    ))} */}
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Confirm"
                  onPress={() => {
                    const selectedWallet =
                      selectedAcc !== null
                        ? accounts[selectedAcc]
                        : accounts[0];

                    if (selectedWallet.id === null) {
                      handleToast(
                        "An error occured. Please try again",
                        "error"
                      );
                    } else {
                      navigation.navigate("OTCPinScreen", {
                        amount,
                        bankDetails,
                        tryAmount,
                        tryRate,
                        wallet: selectedWallet,
                        currency,
                      });
                    }
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>

        {/* WhatsApp Modal */}
        <BottomSheet
          isVisible={showWhatsAppModal}
          showBackArrow={false}
          onClose={() => setShowWhatsAppModal(false)}
          modalContentStyle={{ height: "85%" }}
          extraModalStyle={{ height: "82%" }}
          components={
            <ScrollView contentContainerStyle={{ paddingBottom: 100 }}>
              <View style={styles.whatsAppModalContent}>
                <View style={styles.whatsAppIconContainer}>
                  <SvgXml xml={svg.whatsApp} />
                </View>

                <H4 style={styles.whatsAppTitle}>
                  Proceed to SFx OTC deck on WhatsApp
                </H4>
                <P style={styles.whatsAppSubtitle}>
                  To complete your OTC transaction, please continue on WhatsApp
                </P>

                <View style={styles.checkItemContainer}>
                  <SvgXml xml={svg.checkGreen} width={20} height={20} />
                  <P style={styles.checkItemText}>
                    Share your request with otc on whatsapp{" "}
                    <P style={{ fontFamily: fonts.poppinsBold, fontSize: 12 }}>
                      +************
                    </P>{" "}
                    is the only OTC channel
                  </P>
                </View>

                <View style={styles.checkItemContainer}>
                  <SvgXml xml={svg.checkGreen} width={20} height={20} />
                  <P style={styles.checkItemText}>
                    Send USD from your SFx wallet to your Turkish lira account
                  </P>
                </View>

                <View style={styles.reminderContainer}>
                  <View style={styles.reminderHeader}>
                    <SvgXml xml={svg.alertCircle2} width={16} height={16} />
                    <P style={styles.reminderTitle}>Reminder:</P>
                  </View>
                  <P style={styles.reminderItem}>• Minimum amount: $1 USD</P>
                  <P style={styles.reminderItem}>
                    • Upload your payment receipt for confirmation
                  </P>
                  <P style={styles.reminderItem}>
                    • Your Turkish lira account will be credited after
                    verification
                  </P>
                </View>

                <Button
                  btnText="Start OTC on WhatsApp"
                  onPress={openWhatsApp}
                  loading={loading}
                  style={styles.whatsAppButton}
                />
              </View>
            </ScrollView>
          }
        />
      </Div>
      {loadrer && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    // height: (100 * height) / 100,
    marginBottom: 40,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
    marginTop: 8,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  line: {
    width: "100%",
    // height: 1,
    borderBottomWidth: 1,
    borderStyle: "dashed",
    borderColor: colors.stroke,
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
  },
  whatsAppModalContent: {
    paddingTop: 24,
    paddingBottom: 24,
  },
  whatsAppIconContainer: {
    alignItems: "center",
    marginBottom: 16,
  },
  whatsAppTitle: {
    fontSize: 18,
    fontFamily: fonts.poppinsBold,
    textAlign: "center",
    marginBottom: 8,
  },
  whatsAppSubtitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    textAlign: "center",
    color: colors.gray,
    marginBottom: 24,
  },
  checkItemContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  checkItemText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginLeft: 8,
    flex: 1,
  },
  reminderContainer: {
    backgroundColor: colors.lowOpPrimary3,
    borderLeftWidth: 4,
    borderColor: colors.primary,
    padding: 16,
    borderRadius: 8,
    marginVertical: 16,
  },
  reminderHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  reminderTitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsBold,
    marginLeft: 8,
  },
  reminderItem: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginBottom: 4,
  },
  whatsAppButton: {
    marginTop: 16,
  },
});
