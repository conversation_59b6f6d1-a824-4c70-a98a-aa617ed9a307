import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
  Text,
  ScrollView,
} from "react-native";
import P from "../../components/P";
import Link from "../../components/Link";
import Button from "../../components/Button";
import { fonts } from "../../config/Fonts";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import NoteComponent2 from "../../components/NoteComponent2";

const groupByDate = (items) => {
  return items.reduce((acc, item) => {
    acc[item.date] = acc[item.date] || [];
    acc[item.date].push(item);
    return acc;
  }, {});
};

const baseHeight = 812;
const { width, height } = Dimensions.get("window");
export default function MainRewardScreen({ navigation }) {
  const [isWaitListJoined, setIsWaitListJoined] = useState(false);
  const [stImg, setStImg] = useState(require("../../assets/clock.png"));
  const [stText, setStText] = useState("Sent money is pending");
  const [hideBal, setHideBal] = useState(false);
  const [amount, setAmount] = useState("0.00");
  const [amc, setAmc] = useState("SFx");
  const [seeMore, setSeeMore] = useState(false);
  const [isCardRegistred, setIsCardRegisterd] = useState(false);

  const transactions = [
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "bank account",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "P2P",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point used",
      amount: "5.00",
      type: "mobile money",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "sfx money app",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "water",
      time: "6:00 am",
      date: "Yesterday",
    },
    {
      title: "SFx point used",
      amount: "5.00",
      type: "water",
      time: "6:00 am",
      date: "Yesterday",
    },
  ];

  const dailyActivities = [
    { type: "P2p wallet", pointAmount: 6 },
    { type: "Card", pointAmount: 6 },
    { type: "P2p wallet", pointAmount: 6 },
    { type: "Bank", pointAmount: 6 },
    { type: "Card", pointAmount: 6 },
    { type: "SFx money", pointAmount: 6 },
  ];
  const rHistory = [
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "bank account",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point earned",
      amount: "5.00",
      type: "P2P",
      time: "6:00 am",
      date: "Today",
    },
    {
      title: "SFx point used",
      amount: "5.00",
      type: "mobile money",
      time: "6:00 am",
      date: "Today",
    },
  ];
  const groupedTransactions = groupByDate(transactions);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          navigation={navigation}
          text="Reward"
          iconComp={
            <View style={{ position: "absolute", right: 0 }}>
              <Link
                style={{
                  fontSize: 12,
                  fontFamily: fonts.poppinsMedium,
                  textDecorationLine: "underline",
                }}
                onPress={() => {
                  navigation.navigate("Rules");
                }}
              >
                Rules
              </Link>
            </View>
          }
        />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: "15%" }}
        >
          <View style={styles.itemBox}>
            <View style={styles.accountBalance}>
              <SvgXml xml={svg.coin} style={{ marginRight: 8 }} />
              <View>
                <P style={{ fontSize: 12, color: colors.gray }}>SFx point</P>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    width: "100%",
                    alignItems: "center",

                    //   backgroundColor:"red"
                  }}
                >
                  {/* @ts-ignore */}
                  <TouchableOpacity onPress={() => toggleModal()}>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <P style={{ fontSize: 24, lineHeight: 36 }}>
                        {hideBal ? "******" : `${amount}`}
                        {""}
                        <Text
                          style={{
                            fontSize: 12,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {hideBal ? "***" : amc}
                        </Text>
                      </P>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
          <View style={{ width: "90%", alignSelf: "center", marginTop: 16 }}>
            <NoteComponent2
              text={
                "SFx Points are rewards earned for eligible referrals."
              }
            />
          </View>
          {/* <View
            style={{
              width: "90%",
              alignSelf: "center",
              backgroundColor: colors.white,
              borderRadius: 12,
              marginTop: 16,
            }}
          >
            <P
              style={{
                paddingBottom: 12,
                paddingTop: 12,
                paddingLeft: 16,
                fontSize: 12,
                color: colors.gray,
                borderBottomWidth: 1,
                borderColor: colors.stroke,
              }}
            >
              Daily activities
            </P>
            <View>
              {dailyActivities.length === 0 ? (
                <View style={styles.emptyCont}>
                  <SvgXml
                    xml={svg.walletFace}
                    style={{ marginTop: (2 * height) / 100, marginBottom: 16 }}
                  />
                  <P
                    style={{
                      fontFamily: fonts.poppinsMedium,
                      marginBottom: 4,
                      fontSize: 12,
                    }}
                  >
                    No activity!
                  </P>
                  <P
                    style={{
                      color: "#A5A1A1",
                      fontFamily: fonts.poppinsRegular,
                      fontSize: 12,
                    }}
                  >
                    Oops no daily activity today
                  </P>
                </View>
              ) : (
                <>
                  {(seeMore
                    ? dailyActivities
                    : dailyActivities.slice(0, 3)
                  ).map((item, index) => (
                    <View
                      style={[
                        styles.taskItem,
                        {
                          borderColor:
                            !seeMore && index === 2
                              ? "transparent"
                              : index === dailyActivities.length - 1
                              ? "transparent"
                              : colors.stroke,
                        },
                      ]}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        <SvgXml
                          xml={
                            item.type.includes("P2p")
                              ? svg.p2p
                              : item.type.includes("Bank")
                              ? svg.bankGreen
                              : item.type.includes("Card")
                              ? svg.cardF
                              : item.type.includes("SFx money")
                              ? svg.smalllogo
                              : svg.mobile
                          }
                          style={{ marginRight: 8 }}
                        />
                        <View>
                          <P style={{ fontSize: 12 }}>{item.type}</P>
                          <P
                            style={{
                              fontSize: 12,
                              color: colors.gray,
                              fontFamily: fonts.poppinsRegular,
                            }}
                          >
                            Send money and get{" "}
                            <P
                              style={{
                                fontSize: 12,
                                color: colors.primary,
                              }}
                            >
                              {item.pointAmount}%
                            </P>{" "}
                            SFxp
                          </P>
                        </View>
                      </View>
                      <TouchableOpacity
                        style={styles.goBtn}
                        onPress={() => {
                          item.type.includes("P2p")
                            ? navigation.navigate("P2pScreen")
                            : item.type.includes("Bank")
                            ? navigation.navigate("BankMobileMoneyScreen2")
                            : item.type.includes("Card")
                            ? isCardRegistred
                              ? navigation.navigate("CardApplicationScreen")
                              : navigation.navigate("CardScreen")
                            : item.type.includes("SFx money")
                            ? navigation.navigate("AccountDetailsEntry")
                            : navigation.navigate("MobileMoneyScreen2");
                        }}
                      >
                        <P
                          style={{
                            fontSize: 12,
                            color: colors.primary,
                          }}
                        >
                          Go
                        </P>
                      </TouchableOpacity>
                    </View>
                  ))}
                  <View
                    style={{
                      width: "100%",
                      alignItems: "center",
                      justifyContent: "center",
                      marginTop: 12,
                      marginBottom: 12,
                    }}
                  >
                    <TouchableOpacity
                      style={{ flexDirection: "row", alignItems: "center" }}
                      onPress={() => {
                        setSeeMore(!seeMore);
                      }}
                    >
                      <P style={{ fontSize: 12, color: colors.gray }}>
                        {seeMore ? "See less" : "See more"}
                      </P>
                      <SvgXml xml={seeMore ? svg.dropUp : svg.dropDown1} />
                    </TouchableOpacity>
                  </View>
                </>
              )}
            </View>
          </View> */}

          <View
            style={{
              width: "90%",
              alignSelf: "center",
              backgroundColor: colors.white,
              borderRadius: 12,
              marginTop: 16,
            }}
          >
            <View
              style={{
                paddingBottom: 12,
                paddingTop: 12,
                borderBottomWidth: 1,
                borderColor: colors.stroke,
                paddingLeft: 16,
                paddingRight: 16,
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              <P
                style={{
                  fontSize: 12,
                  color: colors.gray,
                }}
              >
                Recent history
              </P>
              <Link
                style={{ fontSize: 12, textDecorationLine: "underline" }}
                onPress={() => navigation.navigate("RewardScreen")}
              >
                View all
              </Link>
            </View>
            <View>
              {rHistory.length === 0 ? (
                <View style={styles.emptyCont}>
                  <SvgXml
                    xml={svg.walletFace}
                    style={{ marginTop: (2 * height) / 100, marginBottom: 16 }}
                  />
                  <P
                    style={{
                      fontFamily: fonts.poppinsMedium,
                      marginBottom: 4,
                      fontSize: 12,
                    }}
                  >
                    No reward!
                  </P>
                  <P
                    style={{
                      color: "#A5A1A1",
                      fontFamily: fonts.poppinsRegular,
                      fontSize: 12,
                    }}
                  >
                    you have no reward yet
                  </P>
                </View>
              ) : (
                <>
                  {rHistory.slice(0, 8).map((item, index) => (
                    <TouchableOpacity
                      onPress={() => {
                        navigation.navigate("PointDetailsScreen", {
                          type: item.title.toLowerCase().includes("earned")
                            ? "earned"
                            : "used",
                        });
                      }}
                    >
                      <View
                        style={[
                          styles.item,
                          // {
                          //   borderBottomWidth:
                          //     index === rHistory.length - 1 ? 0 : 1,
                          // },
                        ]}
                        key={index}
                      >
                        <SvgXml
                          xml={
                            item.title.toLowerCase().includes("earned")
                              ? svg.coin
                              : svg.redCoin
                          }
                        />
                        <View style={{ marginLeft: 12 }}>
                          <P style={styles.transactionAmount}>{item.title}</P>
                          <P style={styles.transactionDate}>{item.time}</P>
                        </View>
                        <View
                          style={{
                            position: "absolute",
                            right: 16,
                            // top: 16,
                            bottom: 16,
                            alignItems: "flex-end",
                          }}
                        >
                          <P
                            style={{
                              fontSize: 12,
                              fontFamily: fonts.poppinsMedium,
                            }}
                          >
                            {item.title.toLowerCase().includes("earned")
                              ? "+"
                              : "-"}
                            {item.amount}
                            <P style={{ fontSize: 10 }}>SFxp</P>
                          </P>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))}
                </>
              )}
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  itemBox: {
    width: "90%",
    alignItems: "center",
    marginTop: 16,
    alignSelf: "center",
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  accountBalance: {
    alignItems: "center",
    flexDirection: "row",
    // backgroundColor:"red",
    width: "100%",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  addMoneyButton: {
    // marginTop: 10,
    // paddingVertical: 8,
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "rgba(140, 82, 255, 1)",
    borderRadius: 20,
  },
  addMoneyText: {
    color: "#fff",
    fontSize: 12,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  item: {
    width: "100%",
    padding: 16,
    alignItems: "center",
    // backgroundColor: colors.white,
    borderColor: colors.stroke,
    borderRadius: 12,
    flexDirection: "row",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  emptyCont: {
    width: "100%",
    height: (30 * height) / 100,
    // backgroundColor: "red",
    alignItems: "center",
    justifyContent: "center",
  },
  datCat: {
    fontSize: 12,
  },
  taskItem: {
    paddingTop: 12,
    paddingBottom: 12,
    paddingRight: 16,
    paddingLeft: 16,
    borderBottomWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  goBtn: {
    width: 48,
    height: 24,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
});
