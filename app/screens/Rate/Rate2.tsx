import React, { useState, useContext, useCallback, useEffect } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { countries } from "../../components/counties";
import { fonts } from "../../config/Fonts";
import { svg } from "../../config/Svg";
import NoteComponent2 from "../../components/NoteComponent2";
import i18n from "../../../i18n";
import P from "../../components/P";
import { LanguageContext } from "../../context/LanguageContext";
import { useFocusEffect } from "@react-navigation/native";
import { GetAllRate } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { GetAllSFxRate } from "../../RequestHandlers/Wallet";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");

export default function Rate2({ navigation }) {
  const [searchQuery, setSearchQuery] = useState<string>("");
  // @ts-ignore
  const { language, changeLanguage } = useContext(LanguageContext);
  const [allRate, setAllRate] = useState([]);
  const [filteredCountries, setFilteredCountries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeRate, setActiveRate] = useState<any>({
    country: "United States",
    flag: require("../../assets/usa.png"),
    symbol: "$",
    currencyCode: "USD",
  });
  const rContries = [
    {
      country: "United States",
      flag: require("../../assets/usa.png"),
      symbol: "$",
      currencyCode: "USD",
    },
    {
      country: "Turkey",
      flag: require("../../assets/turkey.png"),
      symbol: "₺",
      currencyCode: "TRY",
    },
  ];

  useFocusEffect(
    useCallback(() => {
      getAllRates();
    }, [activeRate?.currencyCode])
  );

  const getAllRates = async () => {
    setLoading(true);
    try {
      const sfxRates = await GetAllSFxRate(activeRate.currencyCode);
      const ratesByPairAndProvider = {};

      // First, group all rates by currency pair and type (buy/sell)
      sfxRates.forEach(
        (rate: {
          from: string;
          to: string;
          type: string;
          amount: number;
          provider: string;
        }) => {
          const key = `${rate.from}-${rate.to}`;
          if (!ratesByPairAndProvider[key]) {
            ratesByPairAndProvider[key] = {};
          }
          if (!ratesByPairAndProvider[key][rate.type]) {
            ratesByPairAndProvider[key][rate.type] = [];
          }
          ratesByPairAndProvider[key][rate.type].push(rate);
        }
      );
      // Then create a map with prioritized rates
      const ratesMap = {};

      // Process each currency pair
      Object.keys(ratesByPairAndProvider).forEach((pair) => {
        ratesMap[pair] = {};

        // Process each rate type (buy/sell)
        Object.keys(ratesByPairAndProvider[pair]).forEach((type) => {
          const rates = ratesByPairAndProvider[pair][type];

          // If there are multiple providers for this pair and type
          if (rates.length > 1) {
            // Prioritize sfx-yellow-card over sfx-link
            const yellowCardRate = rates.find(
              (r: { provider: string; amount: number }) =>
                r.provider === "sfx-yellow-card" || r.provider === "yellow-card"
            );
            if (yellowCardRate) {
              ratesMap[pair][type] = yellowCardRate.amount;
            } else {
              // If no yellow-card rate, use the first available rate
              ratesMap[pair][type] = rates[0].amount;
            }
          } else if (rates.length === 1) {
            // If only one provider, use that rate
            ratesMap[pair][type] = rates[0].amount;
          }
        });
      });
      const appendCountry = countries;
      let matchedRates = appendCountry
        .filter((country) => {
          const key = `${activeRate.currencyCode}-${country.currencyCode}`;
          return ratesMap[key];
        })
        .map((country) => {
          const keySell =
            country.currencyCode === "USD"
              ? `${country.currencyCode}-${activeRate.currencyCode}`
              : `${activeRate.currencyCode}-${country.currencyCode}`;
          const keyBuy =
            country.currencyCode === "USD"
              ? `${activeRate.currencyCode}-${country.currencyCode}`
              : `${country.currencyCode}-${activeRate.currencyCode}`;
          const sellrate = ratesMap[keySell];
          const buyrate = ratesMap[keyBuy];
          return {
            ...country,
            buyRate: buyrate?.buy || "N/A",
            sellRate: sellrate?.sell || "N/A",
          };
        });

      setFilteredCountries(matchedRates);
      setAllRate(matchedRates);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  // Filter the countries based on the search query
  useEffect(() => {
    if (searchQuery) {
      // Filter countries based on the search query
      const filtered = allRate.filter((country) =>
        country.country.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCountries(filtered);
    } else {
      setFilteredCountries(allRate); // Reset to all countries when the search query is empty
    }
  }, [searchQuery, allRate]);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text={i18n.t("bComp.rate")}
          navigation={navigation}
        />
        <View style={styles.customInput}>
          <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
          <TextInput
            style={styles.input}
            placeholder="Search"
            cursorColor={colors.black}
            value={searchQuery}
            onChangeText={(text) => setSearchQuery(text)}
          />
        </View>
        <View
          style={{
            marginTop: 32,
            width: "90%",
            alignSelf: "center",
            marginBottom: 16,
          }}
        >
          <NoteComponent2 text={i18n.t("exchangeRate")} />
        </View>
        <ScrollView>
          <View
            style={{
              width: "90%",
              backgroundColor: colors.white,
              alignSelf: "center",
              borderRadius: 12,
              marginBottom: 200,
            }}
          >
            <View
              style={{ paddingTop: 12, paddingBottom: 12, paddingLeft: 16 }}
            >
              <P style={{ fontSize: 13, color: colors.dGray }}>Exchange rate</P>
            </View>
            <View
              style={{
                width: "100%",
                flexDirection: "row",
                alignItems: "center",
                paddingLeft: 16,
                paddingRight: 16,
                justifyContent: "space-between",
                marginBottom: 12,
              }}
            >
              {rContries.map((item) => (
                <TouchableOpacity
                  key={`currency-option-${item.currencyCode}`}
                  style={{
                    width: "48%",
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    borderWidth: 1,
                    borderColor:
                      activeRate.symbol == item.symbol
                        ? colors.primary
                        : colors.stroke,
                    borderRadius: 8,
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                  onPress={() => {
                    setActiveRate(item);
                  }}
                >
                  <Image
                    source={item.flag}
                    style={{
                      width: 24,
                      height: 24,
                      borderRadius: 100,
                      marginRight: 8,
                    }}
                  />
                  <P style={{ fontSize: 12 }} numberOfLines={1}>
                    {item.currencyCode}
                  </P>
                  <SvgXml
                    xml={
                      item.symbol === activeRate.symbol
                        ? svg.checked
                        : svg.check
                    }
                    style={{ position: "absolute", right: 16 }}
                  />
                </TouchableOpacity>
              ))}
            </View>
            <View style={styles.rateNav}>
              <P style={{ fontSize: 13, color: colors.dGray, paddingLeft: 16 }}>
                Currency
              </P>
              <View style={{ flexDirection: "row", alignItems: "center", width: "50%", justifyContent: "space-around" }}>
                <P style={styles.add}>Add</P>
                <P style={styles.add}>Send</P>
              </View>
            </View>
            <View>
              {filteredCountries.length === 0 ? (
                <></>
              ) : (
                filteredCountries.map((item, index) => (
                  <React.Fragment key={`country-${item.currencyCode}-${index}`}>
                    <View
                      style={[
                        styles.rateItem,
                        {
                          borderBottomWidth:
                            index === filteredCountries.length - 1 ? 0 : 1,
                        },
                      ]}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          width: "50%",
                        }}
                      >
                        <Image
                          source={item.flag}
                          style={{
                            width: 24,
                            height: 24,
                            marginRight: 8,
                            borderRadius: 100,
                            objectFit: "cover",
                          }}
                        />
                        <View>
                          <P
                            style={{ fontSize: 12, width: 120 }}
                            numberOfLines={1}
                          >
                            {item.country}
                          </P>
                          <P
                            style={{ fontSize: 12, width: 120 }}
                            numberOfLines={1}
                          >
                            {item.currencyCode} ~ {activeRate.currencyCode}
                          </P>
                        </View>
                      </View>
                      <View style={{ flexDirection: "row", width: "50%", justifyContent: "space-between" }}>
                        <TouchableOpacity
                          onPress={() =>
                            navigation.navigate("AddMoneyRate2", {
                              from: "AddMoneyRate",
                              activeRate: activeRate,
                              item: item,
                            })
                          }
                          style={{ width: "45%" }}
                        >
                          <View style={styles.rateCont}>
                            <P style={styles.rateText}>
                              {item?.buyRate != "N/A"
                                ? formatToTwoDecimals(item?.buyRate)
                                : formatToTwoDecimals(item?.buyRate)}
                            </P>
                          </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() =>
                            navigation.navigate("AddMoneyRate2", {
                              from: "SendMoneyRate",
                              item: item,
                              activeRate: activeRate,
                            })
                          }
                          style={{ width: "45%" }}
                        >
                          <View style={[styles.rateCont]}>
                            <P
                              // @ts-ignore
                              style={[
                                styles.rateText,
                                { marginRight: 0, borderColor: colors.red },
                              ]}
                            >
                            {item?.sellRate != "N/A"
                            ? formatToTwoDecimals(item?.sellRate)
                            : formatToTwoDecimals(item.sellRate)}
                            </P>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </React.Fragment>
                ))
              )}
            </View>
          </View>
        </ScrollView>
      </Div>
      {loading && <Loader loading={true} />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  customInput: {
    width: "90%",
    height: 44,
    backgroundColor: colors.white,
    alignSelf: "center",
    borderRadius: 12,
    marginTop: 16,
    paddingLeft: 16,
    paddingRight: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    width: "80%",
    height: 44,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
  },
  rateNav: {
    width: "100%",
    backgroundColor: colors.secBackground,
    flexDirection: "row",
    paddingTop: 12.5,
    paddingBottom: 12.5,
    justifyContent: "space-between",
  },
  rateItem: {
    width: "100%",
    paddingVertical: 24,
    paddingHorizontal: 24,
    flexDirection: "row",
    justifyContent: "space-between",
    borderBottomWidth: 1,
    borderBottomColor: colors.stroke,
  },
  rateText: {
    fontSize: 11,
    borderWidth: 0,
    textAlign: "center",
  },
  add: {
    fontSize: 12,
    // paddingLeft: 30,
    // paddingRight: 30,
  },
  rateCont: {
    width: "100%",
    height: 34,
    borderWidth: 1,
    borderColor: colors.green,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
});
