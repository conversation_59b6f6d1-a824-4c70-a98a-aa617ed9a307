import React, { useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
  Text,
  ScrollView,
  FlatList,
  ActivityIndicator,
  Platform,
  Share,
} from "react-native";
import P from "../../components/P";
import Link from "../../components/Link";
import Button from "../../components/Button";
import { fonts } from "../../config/Fonts";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import {
  ClaimSfxPoint,
  GetReferals,
  GetSfxPoint,
} from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import { formatDate2 } from "../../components/FormatDate";
import { formatDate } from "../../components/FormatDate";
import ReferralShare from "../../components/ReferralShare";
import { useToast } from "../../context/ToastContext";
import { LinearGradient } from "expo-linear-gradient";
import BottomSheet from "../../components/BottomSheet";
import * as Clipboard from "expo-clipboard";
import Loader from "../../components/ActivityIndicator";
import AuthenticationHedear2 from "../../components/AuthenticationHedear2";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import DashedBorderView from "../../components/DashedBorderView";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

// const groupByDate = (items) => {
//   return items.reduce((acc, item) => {
//     const dateOnly = new Date(item.createdAt).toISOString().split("T")[0];
//     acc[dateOnly] = acc[dateOnly] || [];
//     acc[dateOnly].push(item);
//     return acc;
//   }, {});
// };
const groupByMonth = (items) => {
  return items.reduce((acc, item) => {
    const date = new Date(item.createdAt);
    const monthKey = `${date.getFullYear()}-${String(
      date.getMonth() + 1
    ).padStart(2, "0")}`; // e.g., "2024-06"
    acc[monthKey] = acc[monthKey] || [];
    acc[monthKey].push(item);
    return acc;
  }, {});
};
const formatGroupMonth = (monthKey) => {
  const [year, month] = monthKey.split("-");
  const date = new Date(`${year}-${month}-01`);
  return date.toLocaleString("default", { month: "long", year: "numeric" }); // e.g., "June 2024"
};

const baseHeight = 812;
const { width, height } = Dimensions.get("window");
export default function ReferralProgramListScreen({ navigation, route }) {
  const [isWaitListJoined, setIsWaitListJoined] = useState(false);
  const [stImg, setStImg] = useState(require("../../assets/clock.png"));
  const [stText, setStText] = useState("Sent money is pending");
  const [hideBal, setHideBal] = useState(false);
  const [amount, setAmount] = useState(0);
  const [amc, setAmc] = useState("");
  const [limit, setLimit] = useState(500);
  const [referrals, setReferrals] = useState<any>([]);
  const [loading1, setLoading1] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [totalItem, setTotalItem] = useState(0);
  const [length, setLength] = useState(0);
  const [loading, setLoading] = useState(false);
  const [actionLoad, setActionLoad] = useState(false);
  const { handleToast } = useToast();
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const referralCode = route.params.code || ""; // Example referral code
  const referralLink = "tFh273/martins/...sfxchan.co";
  const { overView } = route.params || {};
  const [showStep, setShowStep] = useState(false);
  const [toggleState, setToggleState] = useState([0]);
  const [isReferralProgram, setIsReferralProgram] = useState(false);

  const referral = [
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Today",
      sfxPoint: 20,
      img: require("../../assets/user1.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Today",
      sfxPoint: 20,
      img: require("../../assets/user2.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Today",
      sfxPoint: 20,
      img: require("../../assets/user3.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Yesterday",
      sfxPoint: 20,
      img: require("../../assets/user2.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "Yesterday",
      sfxPoint: 20,
      img: require("../../assets/user3.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "25th Aug 2024",
      sfxPoint: 20,
      img: require("../../assets/user2.png"),
    },
    {
      name: "John Doe",
      time: "5:55 am",
      date: "25th Aug 2024",
      sfxPoint: 20,
      img: require("../../assets/user3.png"),
    },
  ];

  const getSfxPoint = async () => {
    try {
      const res = await GetSfxPoint();
      if (res.totalpoints) {
        setAmount(res.totalpoints);
      }
    } catch (error) {}
  };
  const getRef = async (loadMore = false) => {
    setLoading1(true);
    try {
      const res = await withApiErrorToast(GetReferals(1, limit), handleToast);
      if (res) {
        setLoading1(false);
        setTotalItem(res.meta.totalItems);
        setLength(res.items.length);
      }
      if (res.items.length === 0) {
        setHasMoreData(false);
      } else {
        // Sort transactions
        const sortedTransactions = res.items.sort((a, b) => {
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        });
        setReferrals((prevTransactions) =>
          loadMore ? sortedTransactions : sortedTransactions
        );
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const groupedTransactions = groupByMonth(referrals);
  useFocusEffect(
    useCallback(() => {
      setLoading(true);
      setReferrals([]);
      setLimit(20);
      getRef();
      getSfxPoint();
    }, [])
  );
  const fetchMoreTransactions = () => {
    if (!loading && hasMoreData) {
      const newLimit = limit + 20;
      setLimit(newLimit);
      getRef(true);
    }
  };

  const claimSfxpoint = async () => {
    setActionLoad(true);
    try {
      const res = await ClaimSfxPoint();
      if (res.error) {
        handleToast(res.message, "error");
      } else {
        handleToast(res.message, "success");
      }
    } catch (error) {
    } finally {
      setActionLoad(false);
    }
  };
  function ensureHttps(url) {
    if (url?.startsWith("http://")) {
      return url?.replace("http://", "https://");
    }
    return url;
  }

  const onShare = async () => {
    try {
      const result = await Share.share({
        message: `invite your friends to signup and join the sfx money app community with your referral code ${referralCode}`,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error: any) {
      console.error(error.message);
    }
  };
  const handleToggle = (index) => {
    setToggleState(
      (prev) =>
        prev.includes(index)
          ? prev.filter((i) => i !== index) // Remove if already open
          : [...prev, index] // Add if closed
    );
  };
  const copyToClipboard = async (text) => {
    await Clipboard.setStringAsync(text);
  };
  const renderTransactionItem = ({ item, index }) => (
    <TouchableOpacity disabled={true}>
      <View
        style={[
          styles.item,
          {
            borderBottomWidth: index === referrals.length - 1 ? 0 : 1,
            paddingBottom: index === referrals.length - 1 ? 0 : 12,
          },
        ]}
        key={index}
      >
        <Image
          source={{ uri: ensureHttps(item?.referredUser?.picture) }}
          style={{
            width: 40,
            height: 40,
            borderRadius: 100,
            objectFit: "cover",
          }}
        />
        <View style={{ marginLeft: 12 }}>
          <P style={styles.transactionAmount}>
            {item?.referredUser?.firstName
              ? `${item?.referredUser?.firstName} ${item?.referredUser?.lastName}`
              : "Guest"}
          </P>
          <P style={styles.transactionDate}>{formatDate(item.createdAt)}</P>
        </View>
        <P
          style={{
            position: "absolute",
            right: 16,
            alignSelf: "center",
            fontSize: 12,
            color:
              item?.referredUser?.verified === "true"
                ? colors.green
                : colors.red,
          }}
        >
          {item?.referredUser?.verified === "true" ? "Verified" : "Unverified"}
        </P>
      </View>
    </TouchableOpacity>
  );
  const renderDateGroup = ({ item: date, index }) => (
    <View
      style={{
        backgroundColor: colors.white,
        paddingVertical: 12,
        borderRadius: 12,
        marginBottom: 16,
      }}
    >
      <TouchableOpacity
        onPress={() => {
          handleToggle(index);
        }}
        style={{
          paddingHorizontal: 16,
          borderBottomWidth: toggleState.includes(index) ? 1 : 0,
          borderBottomColor: "#F0EFEF",
          paddingBottom: 12,
          flexDirection: "row",
          alignItems: "center",
        }}
      >
        <P style={styles.datCat}>{formatGroupMonth(date)}</P>
        <SvgXml
          xml={toggleState.includes(index) ? svg.chevronUp : svg.chevronDown}
        />
      </TouchableOpacity>
      {toggleState.includes(index) ? (
        <FlatList
          data={groupedTransactions[date]}
          renderItem={renderTransactionItem}
          keyExtractor={(item, idx) => `${date}-${idx}`}
        />
      ) : null}
    </View>
  );

  const dateKeys = Object.keys(groupedTransactions);

  if (loading) {
    return <Loader />;
  }
  return (
    <>
      <LinearGradient
        colors={["#D0B8FF", "#F7F4FF"]}
        locations={[0.3, 0.3]}
        style={styles.gradient}
      >
        <Image
          source={require("../../assets/innerImg.png")}
          style={styles.bgImg}
        />
        <View style={styles.body}>
          <Div>
            <AuthenticationHedear2
              navigation={navigation}
              text="Referral"
              iconBlack={true}
              iconComp={
                <View style={{ position: "absolute", right: 0 }}>
                  <Link
                    style={{
                      fontSize: 12,
                      fontFamily: fonts.poppinsMedium,
                      textDecorationLine: "underline",
                      color: colors.black,
                    }}
                    onPress={() => {
                      if (isReferralProgram) {
                        navigation.navigate("ContestRuleScreen");
                      } else {
                        navigation.navigate("NewReferralRules");
                      }
                    }}
                  >
                    Referral rules
                  </Link>
                </View>
              }
            />
            {/* New Referral Contest Section */}
            <View style={styles.contentBody}>
              <View style={styles.contestCard}>
                {/* <P style={styles.ct_T}>Referral contest reward</P> */}
                {/* <View style={styles.contestHeader}>
                    <View>
                      <P style={styles.contestSubtitle}>
                        Join SFx referral contest
                      </P>
                      <P style={styles.contestTitle}>Earning amazing price</P>
                    </View>
                    <SvgXml xml={svg.moneyBag} />
                  </View> */}
                <View
                  style={{
                    width: "100%",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <View>
                    <P
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Earned point
                    </P>
                    <P
                      style={{
                        fontSize: 24,
                        lineHeight: 32,
                        fontFamily: fonts.poppinsMedium,
                      }}
                    >
                      {formatToTwoDecimals(Number(amount))}
                      <P
                        style={{
                          fontSize: 12,
                          fontFamily: fonts.poppinsRegular,
                        }}
                      >
                        SFX
                      </P>
                    </P>
                    {amount >= 1000 ? (
                      <TouchableOpacity
                        style={{
                          paddingVertical: 3,
                          paddingHorizontal: 17.5,
                          backgroundColor: colors.primary,
                          borderRadius: 100,
                          marginTop: 4,
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                        onPress={() => {
                          claimSfxpoint();
                        }}
                      >
                        {actionLoad ? (
                          <ActivityIndicator
                            size={"small"}
                            color={colors.white}
                          />
                        ) : (
                          <P style={{ color: colors.white }}>Claim</P>
                        )}
                      </TouchableOpacity>
                    ) : (
                      <></>
                    )}
                  </View>
                  <SvgXml xml={svg.goldBox} />
                </View>
                <View style={styles.dashedDivider} />
            
                <View style={styles.referralStatsContainer}>
                  <View style={styles.statItem}>
                    <P style={styles.statLabel}>Total referral</P>
                    <P style={styles.statValue}>{overView?.total}</P>
                  </View>
                  <View style={styles.statItem}>
                    <P style={styles.statLabel}>Verified</P>
                    <P style={styles.statValue}>{overView?.verified}</P>
                  </View>
                  <View style={styles.statItem}>
                    <P style={styles.statLabel}>Unverified</P>
                    <P style={styles.statValue}>{overView?.unVerified}</P>
                  </View>
                </View>
              </View>
              <View style={{ width: "100%", alignSelf: "center" }}>
                {Object.keys(groupedTransactions).length === 0 ? (
                  <View style={styles.emptyCont}>
                    <SvgXml xml={svg.pplPrimary} style={{ marginBottom: 16 }} />
                    <P
                      style={{
                        fontFamily: fonts.poppinsMedium,
                        marginBottom: 4,
                      }}
                    >
                      No referrals!
                    </P>
                    <P
                      style={{
                        color: "#A5A1A1",
                        fontFamily: fonts.poppinsRegular,
                        fontSize: 11,
                        lineHeight: 14,
                        textAlign: "center",
                      }}
                    >
                      You have no referral yet, copy your{"\n"}referral code and
                      share link
                    </P>
                  </View>
                ) : (
                  <View>
                    {/* {Object.keys(groupedTransactions).map((date, index) => (
                  <View key={index} style={{ marginTop: 24 }}>
                    <P style={styles.datCat}>{date}</P>
                    <View>
                      {groupedTransactions[date].map((item, index) => (
                       
                      ))}
                    </View>
                  </View>
                ))} */}

                    <View
                      style={{
                        width: "100%",
                        alignSelf: "center",
                        // paddingBottom: 300
                        // marginBottom: 300,
                      }}
                    >
                      <FlatList
                        data={Object.keys(groupedTransactions)}
                        renderItem={renderDateGroup}
                        keyExtractor={(date, index) => `group-${index}`}
                        showsVerticalScrollIndicator={false}
                        onEndReached={() => {
                          fetchMoreTransactions();
                        }}
                        style={{ paddingBottom: 100, marginTop: 16 }}
                        onEndReachedThreshold={0.3}
                        contentContainerStyle={{ paddingBottom: 500 }}
                        ListFooterComponent={
                          loading1 && length < totalItem ? (
                            <ActivityIndicator
                              color={colors.primary}
                              style={{ marginTop: 16 }}
                            />
                          ) : null
                        }
                      />
                    </View>
                  </View>
                )}
              </View>
            </View>
            {/* <View
              style={[
                styles.bottomFloat,
                Platform.OS === "ios"
                  ? {
                      shadowColor: colors.gray,
                      shadowOffset: { width: 0, height: 1 },
                      shadowOpacity: 0.5,
                      shadowRadius: 4,
                    }
                  : { elevation: 20 },
              ]}
            >
              <View style={styles.shareComp}>
                <Button
                  style={{ height: 48 }}
                  btnText="Invite now"
                  onPress={() => {
                    setShowBottomSheet(true);
                  }}
                />
              </View>
            </View> */}

            {/* </ScrollView> */}
          </Div>
        </View>
      </LinearGradient>
      <BottomSheet
        isVisible={showBottomSheet}
        showBackArrow={false}
        backspaceText="Important Notice"
        onClose={() => setShowBottomSheet(false)}
        modalContentStyle={{ height: !showStep ? "40%" : "65%" }}
        extraModalStyle={{ height: "62%" }}
        components={
          <ScrollView
            style={styles.bottomSheetContent}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 100 }}
          >
            <TouchableOpacity
              style={[
                styles.howItWorksHeader,
                { marginBottom: showStep ? 8 : 24 },
              ]}
              onPress={() => {
                setShowStep(!showStep);
              }}
            >
              <P style={styles.howItWorksTitle}>How it works?</P>
              <SvgXml xml={showStep ? svg.chevronUp : svg.chevronDown} />
            </TouchableOpacity>
            {showStep && (
              <View style={styles.stepsContainer}>
                <View style={styles.stepItem}>
                  <View style={styles.trackWrap}>
                    <View style={styles.stepNumberContainer}>
                      <P style={styles.stepNumber}>1</P>
                    </View>
                    <View style={styles.line} />
                  </View>
                  <View style={styles.stepTextContent}>
                    <P style={styles.stepTitle}>Share</P>
                    <P style={styles.stepDescription}>
                      Share referral link code with friends & families
                    </P>
                  </View>
                </View>
                <View style={styles.stepItem}>
                  <View style={styles.trackWrap}>
                    <View style={styles.stepNumberContainer}>
                      <P style={styles.stepNumber}>2</P>
                    </View>
                    <View style={styles.line} />
                  </View>
                  <View style={styles.stepTextContent}>
                    <P style={styles.stepTitle}>Register</P>
                    <P style={styles.stepDescription}>
                      They sign up using your link or code
                    </P>
                  </View>
                </View>
                <View style={styles.stepItem}>
                  <View style={styles.trackWrap}>
                    <View style={styles.stepNumberContainer}>
                      <P style={styles.stepNumber}>3</P>
                    </View>
                  </View>
                  <View style={styles.stepTextContent}>
                    <P style={styles.stepTitle}>Earn</P>
                    <P style={styles.stepDescription}>
                      Get 20SFxp when they add $5 or more in their SFx wallet
                    </P>
                  </View>
                </View>
              </View>
            )}
            <View style={styles.shareComp1}>
              <View style={styles.textSection}>
                <P style={{ fontSize: 11, color: colors.dGray }}>
                  Your referral code
                </P>
                <P style={{ fontFamily: fonts.poppinsSemibold, fontSize: 13 }}>
                  {referralCode}
                </P>
              </View>
              <TouchableOpacity
                style={[
                  styles.buttonSection,
                  { backgroundColor: colors.secBackground },
                ]}
                onPress={() => {
                  copyToClipboard(referralCode);
                }}
              >
                <SvgXml xml={svg.copy} width={24} height={24} />
              </TouchableOpacity>
              <View style={styles.overLayBorder}></View>
            </View>
            <View style={[styles.shareComp1, { marginTop: 16 }]}>
              <View style={styles.textSection}>
                <P style={{ fontSize: 11, color: colors.dGray }}>
                  Your referral code
                </P>
                <P style={{ fontFamily: fonts.poppinsSemibold, fontSize: 13 }}>
                  {referralCode}
                </P>
              </View>
              <TouchableOpacity
                style={[styles.buttonSection]}
                onPress={onShare}
              >
                <SvgXml xml={svg.shareWhite} />
              </TouchableOpacity>
              <View style={styles.overLayBorder}></View>
            </View>
          </ScrollView>
        }
      />
    </>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    height: "100%",
  },
  itemBox: {
    width: "90%",
    marginTop: 16,
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center",
    gap: 15,
  },
  statusState: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsMedium,
  },
  stTx: {
    width: "80%",
    fontSize: 12,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 4,
  },
  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  addMoneyButton: {
    // marginTop: 10,
    // paddingVertical: 8,
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "rgba(140, 82, 255, 1)",
    borderRadius: 20,
  },
  addMoneyText: {
    color: "#fff",
    fontSize: 12,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  item: {
    width: "100%",
    paddingTop: 12,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    marginTop: 8,
    flexDirection: "row",
    alignItems: "center",
    borderBottomColor: "#F0EFEF",
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  emptyCont: {
    width: "100%",
    height: (50 * height) / 100,
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
    marginTop: 16,
    // justifyContent: "center",
  },
  datCat: {
    fontSize: 12,
  },
  bottomFloat: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    paddingVertical: 20,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  shareComp: {
    width: "80%",
    height: 48,
  },
  gradient: {
    width,
    height,
    flex: 1,
  },
  bgImg: {
    width: 180,
    height: 180,
    position: "absolute",
    right: 0,
    top: 43,
    zIndex: 0,
    opacity: 0.2,
  },
  contestCard: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 20,
  },
  contestHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  contestTitle: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginTop: 4,
  },
  contestSubtitle: {
    fontSize: 12,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  contestBagImage: {
    width: 100,
    height: 100,
    borderRadius: 12,
    objectFit: "cover",
  },
  dashedDivider: {
    width: "100%",
    height: 0,
    backgroundColor: colors.secBackground,
    borderStyle: "dashed",
    borderWidth: Platform.OS === "ios" ? 1 : 0.8,
    borderColor: colors.stroke,
    marginVertical: 16,
  },
  referralStatsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
  },
  statLabel: {
    fontSize: 12,
    color: colors.dark500,
    fontFamily: fonts.poppinsRegular,
  },
  statValue: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.poppinsMedium,
  },
  contentBody: {
    width: "90%",
    alignSelf: "center",
    marginTop: 8,
  },
  bottomSheetContent: {
    // paddingHorizontal: 20,
    // paddingBottom: 20,
  },
  howItWorksHeader: {
    flexDirection: "row",
    // justifyContent: "center",
    alignItems: "center",
    gap: 4,
    paddingVertical: 15,
    // marginBottom:  8,
  },
  howItWorksTitle: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  stepsContainer: {
    marginBottom: 30,
  },
  stepItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    // marginBottom: 15,
  },
  stepNumberContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#22C26E",
    justifyContent: "center",
    alignItems: "center",
  },
  stepNumber: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.white,
  },
  stepTextContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dark500,
    marginBottom: 2,
  },
  stepDescription: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  referralCodeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.secBackground,
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  referralLabel: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.dGray,
  },
  referralValue: {
    fontSize: 16,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginTop: 5,
  },
  copyButton: {
    padding: 10,
  },
  referralLinkContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingLeft: 15,
    paddingRight: 10,
    height: 70, // Fixed height for alignment
  },
  shareButton: {
    width: 60,
    height: "100%",
    backgroundColor: colors.primary,
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  textSection: {
    width: "80%",
    height: "100%",
    backgroundColor: colors.secBackground,
    paddingLeft: 16,
    justifyContent: "center",
  },
  buttonSection: {
    width: "20%",
    height: "100%",
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
  overLayBorder: {
    width: "100%",
    position: "absolute",
    height: 53,
    borderWidth: 1.2,
    borderRadius: 8,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    pointerEvents: "none",
  },
  line: {
    height: 28,
    width: 2,
    borderRadius: 2,
    backgroundColor: "#CDF4E3",
    marginTop: 4,
    marginBottom: 4,
  },
  trackWrap: {
    alignItems: "center",
    marginRight: 12,
  },
  shareComp1: {
    width: "100%",
    height: 53,
    flexDirection: "row",
    borderRadius: 8,
    overflow: "hidden",
  },
});
