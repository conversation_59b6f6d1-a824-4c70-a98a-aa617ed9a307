import React, { useState } from "react";
import { View, TouchableOpacity, Image } from "react-native";
import * as Clipboard from "expo-clipboard";
import { SvgXml } from "react-native-svg";
import BottomSheet from "../../../components/BottomSheet";
import P from "../../../components/P";
import { colors } from "../../../config/colors";
import { fonts } from "../../../config/Fonts";
import { svg } from "../../../config/Svg";
import { useToast } from "../../../context/ToastContext";
import { AfricanCountriesList } from "../../../components/AfricanCountriesList";

interface ReferralUser {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  homeCountry?: string;
  picture?: string;
  verified?: string;
}

interface ReferralDetailsItem {
  referredUser?: ReferralUser;
  createdAt?: string;
}

interface ReferralDetailsBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  detailsItem?: ReferralDetailsItem;
  formatDate: (date: string) => string;
  ensureHttps: (url: string) => string;
}

export default function ReferralDetailsBottomSheet({
  isVisible,
  onClose,
  detailsItem,
  formatDate,
  ensureHttps,
}: ReferralDetailsBottomSheetProps) {
  const [isEmailCopied, setIsEmailCopied] = useState(false);
  const [isPhoneCopied, setIsPhoneCopied] = useState(false);
  const { handleToast } = useToast();

  const copyEmail = async () => {
    if (detailsItem?.referredUser?.email) {
      try {
        await Clipboard.setStringAsync(detailsItem.referredUser.email);
        setIsEmailCopied(true);
        handleToast("Email copied to clipboard", "success");
        setTimeout(() => setIsEmailCopied(false), 2000);
      } catch (error) {
        handleToast("Failed to copy email", "error");
      }
    }
  };

  const copyPhone = async () => {
    if (detailsItem?.referredUser?.phoneNumber) {
      try {
        await Clipboard.setStringAsync(detailsItem.referredUser.phoneNumber);
        setIsPhoneCopied(true);
        handleToast("Phone number copied to clipboard", "success");
        setTimeout(() => setIsPhoneCopied(false), 2000);
      } catch (error) {
        handleToast("Failed to copy phone number", "error");
      }
    }
  };

  return (
    <BottomSheet
      isVisible={isVisible}
      backspaceText={"Details"}
      onClose={onClose}
      showBackArrow={false}
      components={
        <View style={{ marginTop: 32 }}>
          <View
            style={{
              borderRadius: 12,
              backgroundColor: colors.white,
              flexDirection: "row",
              gap: 12,
              alignItems: "center",
              padding: 16,
              // Add shadow for iOS
              shadowColor: "rgba(140, 82, 255, 0.12)",
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 1,
              shadowRadius: 16,
              // Add elevation for Android
              elevation: 8,
            }}
          >
            <Image
              source={
                detailsItem?.referredUser?.picture
                  ? {
                      uri: ensureHttps(detailsItem?.referredUser?.picture),
                    }
                  : require("../../../assets/user1.png")
              }
              style={{
                width: 50,
                height: 50,
                borderRadius: 100,
                objectFit: "cover",
              }}
            />
            <View style={{ alignItems: "flex-start" }}>
              <P style={{ fontSize: 12 }}>
                {detailsItem?.referredUser?.firstName
                  ? `${detailsItem?.referredUser?.firstName} ${detailsItem?.referredUser?.lastName}`
                  : "Guest"}
              </P>
              {detailsItem?.referredUser?.homeCountry && (() => {
                // Find the country code from AfricanCountriesList
                const matchedCountry = AfricanCountriesList.find(
                  (country) => country.name === detailsItem?.referredUser?.homeCountry
                );

                console.log(detailsItem?.referredUser?.homeCountry);
                

                return (
                  <View style={{ flexDirection: "row", alignItems: "center", marginTop: 2 }}>
                    {matchedCountry && (
                      <Image
                        source={{
                          uri: `https://flagcdn.com/w2560/${matchedCountry.code.toLowerCase()}.png`,
                        }}

                        style={{
                          width: 16,
                          height: 16,
                          marginRight: 6,
                          borderRadius: 100,
                          objectFit: matchedCountry.code === "NG" ? "fill" : "cover",
                        }}
                      />
                    )}
                    <P
                      style={{
                        fontSize: 12,
                        color: colors.dark500,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      {detailsItem?.referredUser?.homeCountry}
                    </P>
                  </View>
                );
              })()}
              <P
                style={{
                  fontSize: 12,
                  color: colors.dGray,
                  fontFamily: fonts.poppinsRegular,
                  marginTop: 2,
                }}
              >
                {formatDate(detailsItem?.createdAt)}
              </P>
              <View
                style={{
                  width: "auto",
                  paddingHorizontal: 8,
                  paddingVertical: 1.5,
                  borderRadius: 100,
                  backgroundColor:
                    detailsItem?.referredUser?.verified === "true"
                      ? "#C1FEE4"
                      : "#FFDFE0",
                  marginTop: 6,
                }}
              >
                <P
                  style={{
                    fontSize: 12,
                    fontFamily: fonts.poppinsMedium,
                    color:
                      detailsItem?.referredUser?.verified === "true"
                        ? colors.green
                        : colors.red,
                  }}
                >
                  {detailsItem?.referredUser?.verified === "true"
                    ? "Verified"
                    : "Unverified"}
                </P>
              </View>
            </View>
          </View>
          <View style={{ marginTop: 32 }}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  gap: 8,
                  alignItems: "center",
                }}
              >
                <SvgXml xml={svg.mail} />
                <View>
                  <P
                    style={{
                      fontSize: 12,
                      color: colors.dark500,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    Email address
                  </P>
                  <P
                    style={{
                      fontSize: 12,
                      color: colors.black,
                      fontFamily: fonts.poppinsMedium,
                    }}
                  >
                    {detailsItem?.referredUser?.email}
                  </P>
                </View>
              </View>
              <TouchableOpacity onPress={copyEmail} style={styles.copyBtn}>
                <View style={styles.copyBtn}>
                  <P style={styles.copyText}>
                    {isEmailCopied ? "Copied" : "Copy"}
                  </P>

                  <SvgXml
                    xml={isEmailCopied ? svg.circleSuccess : svg.copy}
                    style={{ width: 14, height: 14 }}
                  />
                </View>
              </TouchableOpacity>
            </View>
            {detailsItem?.referredUser?.phoneNumber && (
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginTop: 24,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    gap: 8,
                    alignItems: "center",
                  }}
                >
                  <SvgXml xml={svg.call2} />
                  <View>
                    <P
                      style={{
                        fontSize: 12,
                        color: colors.dark500,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Phone number
                    </P>
                    <P
                      style={{
                        fontSize: 12,
                        color: colors.black,
                        fontFamily: fonts.poppinsMedium,
                      }}
                    >
                      {detailsItem?.referredUser?.phoneNumber}
                    </P>
                  </View>
                </View>
                <TouchableOpacity onPress={copyPhone} style={styles.copyBtn}>
                  <View style={styles.copyBtn}>
                    <P style={styles.copyText}>
                      {isPhoneCopied ? "Copied" : "Copy"}
                    </P>

                    <SvgXml
                      xml={isPhoneCopied ? svg.circleSuccess : svg.copy}
                      style={{ width: 14, height: 14 }}
                    />
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      }
      modalContentStyle={{ height: "70%" }}
      extraModalStyle={{ height: "77%" }}
      componentHolderStyle={{ flex: 1 }}
    />
  );
}

const styles = {
  copyBtn: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 100,
    backgroundColor: colors.lowOpPrimary2,
  },
  copyText: {
    fontSize: 12,
    color: colors.primary,
    fontFamily: fonts.poppinsMedium,
  },
};
