import React, { useState, useEffect } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Image } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import InputCard from "../../components/InputCard";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { colors } from "../../config/colors";
import { GetRateById } from "../../RequestHandlers/Wallet";
import { GetRateByCountry } from "../../RequestHandlers/Wallet";
import { GetYellowCardEstimatedFee } from "../../RequestHandlers/Wallet";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import useDebounce from "../../components/Debounce";
import { useToast } from "../../context/ToastContext";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");

export default function AddMoneyAmountScreen2({ navigation, route }) {
  const {
    currencyCode,
    symbol,
    networkID,
    channelID,
    country,
    provider,
    phone,
    accName,
    max,
    min,
    aplhaCode2,
  } = route?.params;
  const [inputValue, setInputValue] = useState("0");
  const [loader, setLoader] = useState(false);
  const [error, setError] = useState(false);
  const [isUsdInput, setIsUsdInput] = useState(true); // Track which currency is being input
  const [ngnRate, setNgnRate] = useState(0);
  const [localAmount, setLocalAmount] = useState(0.0);
  const [localFee, setLocalFee] = useState(0);
  const [fee, setFee] = useState<any>([]);
  const debouncedValue = useDebounce(Number(inputValue), 500);
  const debouncedValue2 = useDebounce(Number(inputValue) / ngnRate, 500);
  const { handleToast } = useToast();
  const formatNumber = (value) => {
    value = value.toString();
    return value.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const formatNumberWithDecimal = (value, decimalPlaces = 2) => {
    if (!isNaN(value)) {
      return Number(value)
        .toFixed(decimalPlaces)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return "0.00";
  };

  const handleKeyPress = (key) => {
    setError(false); // Reset error state on key press

    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0"); // Reset input value when toggling
  };

  const convertedValue = isUsdInput
    ? formatNumberWithDecimal(Number(inputValue) * ngnRate)
    : formatNumberWithDecimal(Number(inputValue) / ngnRate);

  const getRateById = async () => {
    try {
      const rate = await GetRateById(currencyCode);
      const sfxRate = await GetRateByCountry(currencyCode, "yellow-card");
      sfxRate.map((item, index) => {
        if (item.type === "buy") {
          setNgnRate(item.amount);
        }
      });
      // if (rate) {
      //   setNgnRate(rate[0].buy);
      // }
    } catch (error) {}
  };
  const getFee = async () => {
    try {
      const res = await GetYellowCardEstimatedFee(
        aplhaCode2,
        isUsdInput ? debouncedValue : debouncedValue2,
        "momo",
        "DEPOSIT"
      );
      if (res.fee) {
        setNgnRate(res.rate);
        setFee(res);
        if (Number(inputValue) === 0) {
          setLocalAmount(0.0);
          setLocalFee(0);
        } else {
          setLocalAmount(res.totalAmountInLocal);
          if (isUsdInput) {
          }
          setLocalFee(res.feeInLocal);
        }
      }
    } catch (error) {}
  };
  useEffect(() => {
    getFee();
  }, [debouncedValue]);

  useEffect(() => {
    getRateById();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <InputCard
                headerText="How much do you want to send"
                onTogglePress={toggleCurrency}
                toggleStyle={{ top: "50%" }}
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 32,
                        lineHeight: 48,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `$${formatNumber(inputValue)}`
                        : `${symbol}${formatNumber(inputValue)}`}
                      <P style={{ lineHeight: 48 }}>
                        {isUsdInput ? "USD" : currencyCode}
                      </P>
                    </P>
                  </>
                }
                convertedValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 16,
                        lineHeight: 24,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `${symbol}${formatNumberWithDecimal(
                            Number(inputValue) * ngnRate
                          )}`
                        : `$${
                            inputValue === "0"
                              ? 0?.toFixed(2)
                              : formatToTwoDecimals(
                                  Number(inputValue) / ngnRate
                                )
                          }`}
                      <P style={{ lineHeight: 24, fontSize: 12 }}>
                        {isUsdInput ? currencyCode : "USD"}
                      </P>
                    </P>
                  </>
                }
                extraComponent={
                  <View style={styles.fee}>
                    <View style={{ width: 24, alignItems: "center" }}>
                      <SvgXml xml={svg.coin1} style={{ marginRight: 8 }} />
                    </View>
                    <P style={{ fontSize: 12, color: colors.gray }}>
                      Charges: {formatNumberWithDecimal(localFee) || 0}{" "}
                      {currencyCode}
                    </P>
                  </View>
                }
                text2={`Exchange rate: 1 USD ~ ${ngnRate} ${currencyCode}`}
                error={error}
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
              <View
                style={{ width: "80%", alignSelf: "center", marginTop: 16 }}
              >
                <Button
                  btnText="Next"
                  loading={loader}
                  disabled={ngnRate === 0}
                  onPress={() => {
                    if (inputValue === "0") {
                      setError(true);
                      return;
                    }

                    // Convert input to local currency amount
                    const localAmount = isUsdInput
                      ? Number(inputValue) * ngnRate
                      : Number(inputValue);

                    // Calculate effective minimum (default to $3 USD if no min from API)
                    let effectiveMinLocal = min !== 0 ? min : 3 * ngnRate;
                    let effectiveMinUSD = effectiveMinLocal / ngnRate;

                    // Enforce $1 minimum
                    if (effectiveMinUSD < 3) {
                      effectiveMinUSD = 3;
                      effectiveMinLocal = 3 * ngnRate;
                    }

                    // Calculate effective maximum (capped at $3000 USD)
                    const maxUSD = max !== 0 ? max / ngnRate : 0;
                    const effectiveMaxUSD = maxUSD > 3000 ? 3000 : maxUSD;
                    const effectiveMaxLocal = effectiveMaxUSD * ngnRate;

                    // Check limits
                    if (localAmount > effectiveMaxLocal) {
                      handleToast(
                        `The maximum amount to deposit is ${
                          isUsdInput ? "$" : symbol
                        }${
                          isUsdInput
                            ? effectiveMaxUSD.toFixed(2)
                            : formatNumberWithDecimal(effectiveMaxLocal)
                        }${isUsdInput ? "USD" : currencyCode}`,
                        "error"
                      );
                      setError(true);
                      return;
                    }

                    if (localAmount < effectiveMinLocal) {
                      handleToast(
                        `The minimum amount to deposit is ${
                          isUsdInput ? "$" : symbol
                        }${
                          isUsdInput
                            ? effectiveMinUSD.toFixed(2)
                            : formatNumberWithDecimal(effectiveMinLocal)
                        }${isUsdInput ? "USD" : currencyCode}`,
                        "error"
                      );
                      setError(true);
                      return;
                    }

                    // Proceed to next screen if validation passes
                    navigation.navigate("ConfirmDetailScreen2", {
                      country,
                      inputValue: isUsdInput
                        ? Number(inputValue)
                        : Number(inputValue) / ngnRate,
                      ngnRate,
                      currencyCode,
                      channelID,
                      networkID,
                      symbol,
                      provider,
                      phone,
                      accName,
                      aplhaCode2,
                      fee,
                      isUsdInput,
                    });
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}
const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
  },
  bottom: {
    width,
    flex: 1,
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.15),
    marginTop: 16,
  },
  fee: {
    flexDirection: "row",
    alignItems: "center",
    // width: "100%"
    padding: 4,
    marginTop: 8,
    paddingLeft: 0,
  },
});
