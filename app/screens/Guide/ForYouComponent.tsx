import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  Linking,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { SvgXml } from "react-native-svg";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { colors } from "../../config/colors";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import { GetUserDetails } from "../../RequestHandlers/User";
import { TouchableWithoutFeedback } from "react-native-gesture-handler";
import { svg2 } from "../../config/Svg2";

interface PProps {
  isCardRegisterd?: true | false;
  navigation: any;
}
const isTablet = Dimensions.get("window").width >= 600;
const { width, height } = Dimensions.get("window");
const SRC_WIDTH = Dimensions.get("window").width;
const cardLength = (327 / 375) * SRC_WIDTH;
const spacing = SRC_WIDTH * 0.03;
export default function ForYouComponent({ navigation }: PProps) {
  const [isCardRegisterd, setIsCardRegisterd] = useState(false);
  const [frameData, setFrameData] = useState([
    {
      id: "1", // Add unique IDs for tracking
      text2: "How to share SFxmoney app",
      image: require("../../assets/image2.png"),
      url: "https://www.youtube.com/shorts/ZrYQSXzFuQs",
      icon: svg.megaPhone,
      close: true,
    },
    {
      id: "2",
      text2: "How to add money with SFx",
      image: require("../../assets/image2.png"),
      url: "https://www.youtube.com/shorts/ZrYQSXzFuQs",
      icon: svg2.megaWallet,
      close: true,
    },
    {
      id: "3",
      text2: "How to verify your money app account",
      image: require("../../assets/image2.png"),
      url: "https://www.youtube.com/shorts/ZrYQSXzFuQs",
      icon: svg2.megaLaw,
      close: true,
    },
  ]);

  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef(null);
  const screenWidth = Dimensions.get("window").width;

  useEffect(() => {
    const loadRemovedItems = async () => {
      const removedData = await AsyncStorage.getItem("removedItems");
      const removedItems = removedData ? JSON.parse(removedData) : [];

      // Filter out removed items
      const now = Date.now();
      const validRemovedItems = removedItems.filter(
        (item) => now - item.timestamp < 24 * 60 * 60 * 1000 // 24 hours
      );

      const validRemovedIds = validRemovedItems.map((item) => item.id);
      const updatedFrameData = frameData.filter(
        (item) => !validRemovedIds.includes(item.id)
      );

      setFrameData(updatedFrameData);

      // Save valid removed items back to storage
      await AsyncStorage.setItem(
        "removedItems",
        JSON.stringify(validRemovedItems)
      );
    };

    loadRemovedItems();
  }, []);

  useEffect(() => {
    if (frameData.length > 1) {
      const intervalId = setInterval(() => {
        const nextIndex =
          activeIndex === frameData.length - 1 ? 0 : activeIndex + 1;
        setActiveIndex(nextIndex);

        flatListRef.current?.scrollToIndex({
          index: nextIndex,
          animated: nextIndex !== 0, // No animation when resetting to the first item
        });
      }, 5000);

      return () => clearInterval(intervalId);
    }
  }, [activeIndex, frameData.length]);

  const handleRemoveItem = async (indexToRemove) => {
    const removedItem = frameData[indexToRemove];

    setFrameData((prevData) => {
      const updatedData = prevData.filter(
        (_, index) => index !== indexToRemove
      );

      // Ensure activeIndex is valid
      if (activeIndex >= updatedData.length) {
        setActiveIndex((prevIndex) => Math.max(0, prevIndex - 1));
      }
      return updatedData;
    });

    // Save removed item with timestamp
    const removedData = await AsyncStorage.getItem("removedItems");
    const removedItems = removedData ? JSON.parse(removedData) : [];
    removedItems.push({ id: removedItem.id, timestamp: Date.now() });
    await AsyncStorage.setItem("removedItems", JSON.stringify(removedItems));
  };

  const renderItem = ({ item, index }) => (
    <TouchableOpacity
      onPress={() => {
        Linking.openURL(item.url);
      }}
    >
      <View style={{ alignItems: "center" }}>
        <LinearGradient
          colors={["rgba(95, 31, 223, 0.7)", "rgba(229, 57, 57, 0.7)"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[
            styles.card,
            {
              width: screenWidth * 0.88,
              marginBottom: frameData.length === 1 ? 24 : 8,
              marginHorizontal: spacing / 1.5,
              marginLeft:
                index === 0 ? 22 : activeIndex === 1 ? 16 : spacing / 1.5,
              marginRight: index === frameData.length - 1 ? 22 : spacing / 1.5,
            },
          ]}
        >
          <View style={styles.recommendation}>
            <View style={styles.recommendationText}>
              <P
                // @ts-ignore
                style={[
                  styles.recommendationMessage,
                  { width: index === 2 ? 160 : 130 },
                ]}
              >
                {item.text2}
              </P>
            </View>
            <SvgXml xml={item.icon} />
          </View>
        </LinearGradient>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={{ width, alignSelf: "center" }}>
      <View
        style={{
          width: "90%",
          alignSelf: "center",
          marginBottom: 8,
          justifyContent: "space-between",
          flexDirection: "row",
        }}
      >
        <P style={{ fontSize: 12, color: colors.dGray }}>For you</P>
        <TouchableOpacity
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
          }}
          onPress={() => {
            navigation.navigate("AllGuideScreen");
          }}
        >
          <P
            style={{
              fontSize: 12,
              color: colors.primary,
            }}
          >
            View all
          </P>
          <SvgXml xml={svg.arroveRight} />
        </TouchableOpacity>
      </View>
      <FlatList
        data={frameData}
        ref={flatListRef}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
        scrollEnabled={frameData.length > 1}
        onScrollToIndexFailed={() => {}}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(
            event.nativeEvent.contentOffset.x / screenWidth
          );
          setActiveIndex(index);
        }}
      />
      {frameData.length > 1 && (
        <View style={styles.indicatorContainer}>
          {frameData.map((_, index) => (
            <View
              key={index}
              style={[
                styles.indicator,
                {
                  backgroundColor: colors.primary,
                  opacity: activeIndex === index ? 1 : 0.5,
                },
              ]}
            />
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    // marginRight: "5%",
    backgroundColor: "rgba(238, 255, 235, 1)",
    borderRadius: 12,
    padding: 18,
    marginBottom: 8,
    height: 106,
    flexDirection: "row",
    alignItems: "center",
  },
  recommendation: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  recommendationText: {},
  recommendationMessage: {
    fontSize: 14,
    color: colors.white,
    width: 218,
    fontFamily: fonts.poppinsSemibold,
  },
  indicatorContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  indicator: {
    width: 7,
    height: 7,
    marginRight: 5,
    borderRadius: 100,
  },
});
