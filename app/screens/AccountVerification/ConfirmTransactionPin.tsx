import React, { useState, useEffect, useRef, useContext } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  Keyboard,
  TextInput,
} from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import H4 from "../../components/H4";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import { UpdateUser } from "../../RequestHandlers/User";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  ValidateAppleToken,
  ValidateGoogleToken,
} from "../../RequestHandlers/Authentication";
import { useToast } from "../../context/ToastContext";
import { encryptPIN } from "../../Utils/encrypt";
import { updateCredentials } from "../../Utils/credentialsUtils";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");

export default function ConfirmTransactionPin({ navigation, route }) {
  const { pin } = route?.params;
  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const refs = [ref_input1, ref_input2, ref_input3, ref_input4];
  const [fields, setFields] = useState(["", "", "", ""]);
  const [show, setShow] = useState(true);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [pinError, setPinError] = useState(false);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const { handleToast } = useToast();

  const focusNextField = (nextField: any) => {
    nextField.current.focus();
  };

  const handleKeyPress = (index: any, event: any) => {
    const { key, nativeEvent } = event;
    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs[prevIndex]);
        }
      } else {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };

  const handleChangeText = (index: any, text: any) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField(refs[nextIndex]);
        } else {
          if (index === updatedFields.length - 1) {
            Keyboard.dismiss();
          }
        }
      }

      return updatedFields;
    });
  };

  const handlePaste = (index: any, pastedText: string) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      const characters = pastedText.split("");

      for (let i = 0; i < characters.length; i++) {
        const fieldIndex = index + i;
        if (fieldIndex < updatedFields.length) {
          updatedFields[fieldIndex] = characters[i];
        }
      }

      return updatedFields;
    });
  };

  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      // All fields have been filled, call verifyPin
      // handleSubmit();
    }
  }, [fields]);

  const handleFocus = (index: number) => {
    setActiveIndex(index);
  };

  const handleBlur = () => {
    setActiveIndex(null);
  };
  const validateGoogleToken = async (idToken, destination) => {
    try {
      const validateToken = await ValidateGoogleToken({
        idToken: idToken,
      });
      if (validateToken.token) {
        setLoading(false);
        AsyncStorage.setItem("cookies", JSON.stringify(validateToken))
          .then(() => {
            // @ts-ignore
            setStoredCredentails(validateToken);
            // @ts-ignore
            destination === "Home"
              ? navigation.reset({
                  index: 0,
                  routes: [{ name: "BottomTabNavigator" }],
                })
              : navigation.navigate("AccountVerification4");
          })
          .catch((err) => {});
        setTimeout(() => {
          AsyncStorage.removeItem("GToken").then((res) => {});
        }, 3000);
      } else {
        handleToast(validateToken.message, "error");
      }
    } catch (error) {
      setLoading(false);
      handleToast(error.message, "error");
    }
  };
  const validateAppleToken = async (body, destination) => {
    try {
      const validateToken = await ValidateAppleToken(body);
      if (validateToken.token) {
        setLoading(false);
        AsyncStorage.setItem("cookies", JSON.stringify(validateToken))
          .then(() => {
            // @ts-ignore
            setStoredCredentails(validateToken);
            // @ts-ignore
            destination === "Home"
              ? navigation.reset({
                  index: 0,
                  routes: [{ name: "BottomTabNavigator" }],
                })
              : navigation.navigate("AccountVerification4");
          })
          .catch((err) => {});
        setTimeout(() => {
          AsyncStorage.removeItem("AppleLogin").then((res) => {});
        }, 3000);
      } else {
        handleToast(validateToken.message, "error");
      }
    } catch (error) {
      setLoading(false);
      handleToast("Unknown error", "error");
    }
  };
  const upatePin = async (destination) => {
    try {
      const body = {
        pin: await encryptPIN(String(fields.join("").trim())),
      };
      const addPin = await withApiErrorToast(UpdateUser(body), handleToast);
      if (addPin.error) {
        handleToast(addPin.message, "error");
      } else {
        destination === "Home"
          ? navigation.reset({
              index: 0,
              routes: [{ name: "BottomTabNavigator" }],
            })
          : navigation.reset({
              index: 0,
              routes: [{ name: "AccountVerification4" }],
            });
        await updateCredentials(setStoredCredentails, {
          hasPin: true,
        });
        // AsyncStorage.getItem("GToken").then((res) => {
        //   if (res) {
        //     validateGoogleToken(res, destination);
        //   } else {
        //     console.log("No token");
        //   }
        // });
        // AsyncStorage.getItem("AppleLogin").then((res) => {
        //   if (res) {
        //     const objectData = JSON.parse(res);
        //     validateAppleToken(objectData, destination);
        //   }
        // });
      }
    } catch (error) {
      handleToast("Network error try again", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (navigation) => {
    setIsSubmitted(true);
    if (fields.every((field) => field !== "")) {
      if (pin === fields.join("")) {
        setLoading(true);
        upatePin(navigation);
      } else {
        setPinError(true);
      }
    } else {
    }
  };

  const getInputBorderStyle = (index) => {
    return {
      borderColor:
        (fields[index] === "" && isSubmitted) || pinError
          ? colors.red
          : "#E6E5E5",
    };
  };

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          type="KYC"
          text="Account verification"
          navigation={navigation}
          currentStep={3}
          submitFunction={() => handleSubmit("Home")}
          loading={loading}
        />
        <ScrollView>
          <View style={styles.contentCard}>
            <View style={styles.section1Wrap}>
              <H4 style={styles.amt}>Confirm Transaction PIN</H4>
              <H4
                // @ts-ignore
                style={[
                  styles.amt,
                  {
                    fontSize: 14,
                    lineHeight: 18,
                    color: colors.dark500,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  },
                ]}
              >
                Enter your transaction pin again
              </H4>
              <View style={styles.section3Wrap}>
                <View style={styles.con}>
                  {refs.map((ref, index) => (
                    <View
                      style={[
                        styles.pinInput,
                        {
                          borderColor:
                            activeIndex === index
                              ? colors.primary
                              : getInputBorderStyle(index).borderColor,
                        },
                      ]}
                      key={index}
                    >
                      <TextInput
                        style={styles.pinTextInput}
                        placeholderTextColor="#000"
                        keyboardType="numeric"
                        ref={ref}
                        onChangeText={(text) => handleChangeText(index, text)}
                        onKeyPress={(event) => handleKeyPress(index, event)}
                        // @ts-ignore
                        onTextInput={(event) => {
                          const pastedText = event.nativeEvent.text;
                          handlePaste(index, pastedText);
                        }}
                        value={fields[index]}
                        secureTextEntry={show}
                        onFocus={() => handleFocus(index)}
                        onBlur={handleBlur}
                      />
                    </View>
                  ))}
                </View>
                {pinError && <P style={styles.errorText}>Pin does not match</P>}
              </View>
            </View>
          </View>
          <View style={styles.btnCont}>
            <Button
              btnText={"Continue"}
              onPress={() => {
                handleSubmit("acc");
              }}
              loading={loading}
            />
            {/* <Link
              style={{
                textAlign: "center",
                marginTop: (16 / baseHeight) * height,
              }}
              onPress={() => }
            >
              Submit & return home
            </Link> */}
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    borderRadius: 12,
    marginTop: 24,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  amt: {
    fontSize: 20,
    // lineHeight: 48,
    fontFamily: fonts.poppinsSemibold,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: 24,
    borderTopWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  section3Wrap: {
    width: "100%",
    marginTop: 24,
    borderColor: colors.stroke,
    // paddingBottom: 32,
  },
  con: {
    flexDirection: "row",
    justifyContent: "center",
    width: "100%",
    alignSelf: "center",
    gap: 8,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 5,
    width: 58,
    height: 58,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 18,
    // marginBottom: 18 / baseHeight * height,
  },
  pinTextInput: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    width: 58,
    height: 58,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
    marginBottom: 64,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 10,
    width: 264,
    alignSelf: "center",
    marginLeft: 10,
  },
});
