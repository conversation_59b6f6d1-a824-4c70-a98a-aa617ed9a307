import React, { useRef, useState, useEffect } from "react";
import { ScrollView, StyleSheet, View, Dimensions, Image } from "react-native";
import Div from "../../components/Div";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import Input from "../../components/Input";
import BottomComponent from "../../components/BottomComponent";

const screenHeight = Dimensions.get("window").height;
const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function AllSetScreen({ navigation }) {
  const data = [
    {
      text1: "Get started",
      text2:
        "Start using your SFx Points, manage your cards, or make your first transaction now.",
      icon: svg.startUp,
    },
    {
      text1: "Explore features",
      text2:
        "Discover all that SFx has to offer by navigating through the app.",
      icon: svg.holdPhone,
    },
  ];
  return (
    <View style={styles.container}>
      <Div style={{ height: screenHeight }}>
        <ScrollView style={styles.container}>
          <View
            style={{
              width: "85%",
              marginTop: 60,
              alignSelf: "center",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Image
              source={require("../../assets/celeb.png")}
              style={{
                marginTop: (120 / baseHeight) * height,
                width: (80 / baseWidth) * width,
                height: (80 / baseWidth) * width,
              }}
            />
            <P style={styles.text1}>You're all set!</P>
            <P style={styles.text2}>
              You can now enjoy full access to all features and benefits make
              the most of the money app
            </P>
            {data.map((item, i) => (
              <View
                key={i}
                style={{
                  width: "100%",
                  // backgroundColor: 'red',
                  flexDirection: "row",
                  marginTop: (24 / baseHeight) * height,
                  paddingRight: 30,
                }}
              >
                <SvgXml xml={item.icon} style={{ marginRight: 8 }} />
                <View>
                  <P style={{ fontSize: 12 }}>{item.text1}</P>
                  <P
                    style={{
                      fontSize: 12,
                      color: colors.gray,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    {item.text2}
                  </P>
                </View>
              </View>
            ))}
          </View>
          <View style={styles.components}>
            <Button
              btnText="Go to dashboard"
              onPress={() => navigation.navigate("BottomTabNavigator")}
            />
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    // height: screenHeight,
    width,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    marginTop: 20,
    lineHeight: 30,
  },
  text2: {
    fontSize: 12,
    textAlign: "center",
    lineHeight: 22.4,
    color: "#A5A1A1",
    fontFamily: fonts.poppinsRegular,
  },
  components: {
    width: "75%",
    marginTop: 48,
    alignSelf: "center",
    // backgroundColor:"red"
  },
});
