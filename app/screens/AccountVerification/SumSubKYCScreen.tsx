import {
  Dimensions,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { colors } from "../../config/colors";
import SNSMobileSDK from "@sumsub/react-native-mobilesdk-module";
import { GetKYCToken } from "../../RequestHandlers/User";
import { handleToast } from "../../components/Toast";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import { useContext, useEffect, useState } from "react";
import Loader from "../../components/ActivityIndicator";
import NoteComponent2 from "../../components/NoteComponent2";
import { fonts } from "../../config/Fonts";
import P from "../../components/P";
import { SvgXml } from "react-native-svg";
import Button from "../../components/Button";
import { svg } from "../../config/Svg";
import Div from "../../components/Div";
const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");

export default function SumSubKYCScreen({ navigation }: { navigation: any }) {
  const { storedCredentails } = useContext(CredentailsContext);
  const [loading, setLoading] = useState(true);

  const getToken = async (id: string) => {
    setLoading(true);
    try {
      const res = await GetKYCToken(id);
      console.log(res);
      if (res.token) {
        launchSNSMobileSDK(res.token);
      } else {
        handleToast("Failed to get KYC token");
      }
    } catch (error) {
      handleToast(error.message || "Failed to get KYC token");
    } finally {
      setLoading(false);
    }
  };

  // Token refresh handler - uses your existing GetKYCToken endpoint
  const refreshToken = async () => {
    try {
      // @ts-ignore
      const userId = storedCredentails?.user?.id;
      if (userId) {
        const res = await GetKYCToken(userId);
        return res.token;
      }
      throw new Error("No user ID available");
    } catch (error) {
      console.error("Token refresh failed:", error);
      throw error;
    }
  };

  let launchSNSMobileSDK = (accessToken) => {
    console.log("lunched");

    let snsMobileSDK = SNSMobileSDK.init(accessToken, refreshToken)

      .withHandlers({
        // Optional callbacks you can use to get notified of the corresponding events
        onStatusChanged: (event) => {
          console.log(
            "onStatusChanged: [" +
              event.prevStatus +
              "] => [" +
              event.newStatus +
              "]"
          );
        },
        onLog: (event) => {
          console.log("onLog: [Idensic] " + event.message);
        },
      })
      .withDebug(true)
      .withLocale("en") // Optional, for cases when you need to override the system locale
      .withStrings({
        sns_oops_network_title: "Oops! Seems like the network is down.",
        sns_oops_network_html:
          "Please check your internet connection and try again.",
        sns_oops_action_retry: "Try again",
      })
      .build();
    snsMobileSDK
      .launch()
      .then((result) => {
        console.log("SumSub SDK State: " + JSON.stringify(result));
      })
      .catch((err) => {
        console.log("SumSub SDK Error: " + JSON.stringify(err));
      });

    console.log(snsMobileSDK);
  };
  // useEffect(() => {
  //   // @ts-ignore
  //   if (storedCredentails?.user?.id) {
  //     // @ts-ignore
  //     getToken(storedCredentails?.user?.id);
  //   }
  //   // @ts-ignore
  // }, [storedCredentails?.user?.id]);
  const reasons = [
    "Choose a well-lit environment",
    "Place the document on a flat surface",
    "Align the entire document in frame",
    "Avoid filters or edits",
    "Capture and upload document",
  ];
  return (
    <View style={styles.body}>
      <Div>
        <ScrollView contentContainerStyle={{paddingBottom: 50}}>
          <View
            style={{
              width: "90%",
              paddingVertical: 24,
              flexDirection: "row",
              justifyContent: "flex-end",
              alignSelf: "center",
            }}
          >
            <TouchableOpacity
              style={{
                paddingHorizontal: 12,
                paddingVertical: 7,
                borderRadius: 99,
                borderWidth: 1,
                borderColor: colors.stroke,
              }}
              onPress={() => {
                navigation.pop();
              }}
            >
              <P style={{ fontSize: 12 }}>Close</P>
            </TouchableOpacity>
          </View>
          <View
            style={{
              width: "100%",
              alignItems: "center",
              marginTop: (2 * height) / 100,
            }}
          >
            <SvgXml xml={svg.kycFrame} />
            <P style={styles.statusState}>Capture document</P>
            <P style={styles.stTx}>Ensure to capture your full document</P>
            <View style={{ width: "85%", marginTop: 24 }}>
              {reasons.map((item, index) => (
                <View
                  key={index}
                  style={{
                    flexDirection: "row",
                    width: "100%",
                    gap: 8,
                    marginBottom: 4,
                  }}
                >
                  <View style={{ alignItems: "center", gap: 4 }}>
                    <View
                      style={{
                        minWidth: 20,
                        minHeight: 20,
                        backgroundColor: "#22C26E",
                        borderRadius: 100,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <P
                        style={{
                          color: colors.white,
                          textAlign: "center",
                          fontSize: 10,
                        }}
                      >
                        {index + 1}
                      </P>
                    </View>
                    {index !== reasons.length - 1 && (
                      <View
                        style={{
                          width: 2,
                          height: 18,
                          borderRadius: 100,
                          backgroundColor: "#CDF4E3",
                        }}
                      ></View>
                    )}
                  </View>
                  <P
                    style={{
                      flex: 1,
                      fontSize: 14,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    {item}
                  </P>
                </View>
              ))}

              <View style={{ marginTop: 16 }}>
                <NoteComponent2
                  type="red"
                  contStyle={{
                    backgroundColor: colors.redSubtle,
                    marginTop: 12,
                  }}
                  component2={
                    <View>
                      <P style={{ fontSize: 10 }}>Important tips:</P>
                      {[
                        "Do not submit screenshots or scanned copies.",
                        "Do not submit screenshots or scanned copies.",
                        "Document must be valid and not expired.",
                      ].map((item, index) => (
                        <View
                          key={index}
                          style={{
                            flexDirection: "row",
                            gap: 4,
                            paddingLeft: 4,
                          }}
                        >
                          <P style={{ fontSize: 10 }}>•</P>
                          <P
                            style={{
                              fontSize: 10,
                              fontFamily: fonts.poppinsRegular,
                            }}
                          >
                            {item}
                          </P>
                        </View>
                      ))}
                    </View>
                  }
                  // text={"Please double-check your document and try again."}
                />
              </View>
              <View style={{ padding: 16, gap: 6, marginTop: 24, }}>
                <P style={{fontSize: 12, fontFamily: fonts.poppinsRegular}}>
                  Yes, I have read and understood how to capture my document
                  correctly
                </P>
              </View>
              <View
                style={{
                  width: "90%",
                  alignSelf: "center",
                  marginTop: 32,
                }}
              >
                <Button
                  btnText="Continue"
                  loading={loading}
                  onPress={() => {
                    // @ts-ignore
                    getToken(storedCredentails?.user?.id);
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  statusState: {
    fontSize: 20,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 24,
    fontFamily: fonts.poppinsSemibold,
  },
  stTx: {
    fontSize: 14,
    lineHeight: 18,
    // paddingLeft: 50,
    // paddingRight: 50,
    // backgroundColor: 'red',
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.dark500,
    paddingHorizontal: 20,
    marginTop: 4,
  },
});
