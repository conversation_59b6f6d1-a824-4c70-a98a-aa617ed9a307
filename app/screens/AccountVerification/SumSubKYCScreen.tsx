import { StyleSheet, View } from "react-native";
import { colors } from "../../config/colors";
import SNSMobileSDK from "@sumsub/react-native-mobilesdk-module";
import { GetKYCToken } from "../../RequestHandlers/User";
import { handleToast } from "../../components/Toast";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import { useContext, useEffect, useState } from "react";
import Loader from "../../components/ActivityIndicator";

export default function SumSubKYCScreen({ navigation }: { navigation: any }) {
  const { storedCredentails } = useContext(CredentailsContext);
  const [loading, setLoading] = useState(true);

  const getToken = async (id: string) => {
    try {
      const res = await GetKYCToken(id);
      console.log(res);
      if (res.token) {
        launchSNSMobileSDK(res.token);
      } else {
        handleToast("Failed to get KY<PERSON> token");
      }
    } catch (error) {
      handleToast(error.message || "Failed to get KYC token");
    }
  };

  // Token refresh handler - uses your existing GetKYCToken endpoint
  const refreshToken = async () => {
    try {
      // @ts-ignore
      const userId = storedCredentails?.user?.id;
      if (userId) {
        const res = await GetKYCToken(userId);
        return res.token;
      }
      throw new Error("No user ID available");
    } catch (error) {
      console.error("Token refresh failed:", error);
      throw error;
    }
  };

  let launchSNSMobileSDK = (accessToken) => {
    console.log("lunched");
    
    let snsMobileSDK = SNSMobileSDK.init(accessToken, refreshToken)

    
      .withHandlers({
        // Optional callbacks you can use to get notified of the corresponding events
        onStatusChanged: (event) => {
          console.log(
            "onStatusChanged: [" +
              event.prevStatus +
              "] => [" +
              event.newStatus +
              "]"
          );
        },
        onLog: (event) => {
          console.log("onLog: [Idensic] " + event.message);
        },
      })
      .withDebug(true)
      .withLocale("en") // Optional, for cases when you need to override the system locale
      .build();
    snsMobileSDK
      .launch()
      .then((result) => {
        console.log("SumSub SDK State: " + JSON.stringify(result));
      })
      .catch((err) => {
        console.log("SumSub SDK Error: " + JSON.stringify(err));
      });

      console.log(snsMobileSDK);
      
  };
  useEffect(() => {
    // @ts-ignore
    if (storedCredentails?.user?.id) {
      // @ts-ignore
      getToken(storedCredentails?.user?.id);
    }
    // @ts-ignore
  }, [storedCredentails?.user?.id]);
  return <View style={styles.body}></View>;
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
});
