import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Switch,
  Text,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import NoteComponent from "../../components/NoteComponent";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import Button from "../../components/Button";
import ListItemSelect from "../../components/ListItemSelect";
import ListItemSelect2 from "../../components/ListItemSelect2";
import BenediciaryComponent from "../../components/BeneficiaryComponent";
import CustomSwitch from "../../components/CustomSwitch";
import {
  NotMMCountires,
  NotMMPauOutCountries,
} from "../../components/NotMMCountries";
import { GetNetwork } from "../../RequestHandlers/Wallet";
import { GetChannels } from "../../RequestHandlers/Wallet";
import Loader from "../../components/ActivityIndicator";
import { AddBeneficiary } from "../../RequestHandlers/User";
import { GetBeneficiaries } from "../../RequestHandlers/User";
import ContentLoader, { Rect } from "react-content-loader/native";
import CustomSwitch1 from "../../components/CustomSwitch1";
import { countries } from "../../components/counties";
import { useFocusEffect } from "@react-navigation/native";
import { useToast } from "../../context/ToastContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function MobileMoneyScreen2({ navigation }) {
  const { handleToast } = useToast();
  const [countryCode2, setCountryCode2] = useState("");
  const [moneyIcon, setMoneyIcon] = useState(require("../../assets/momo.png"));
  const [show2, setShow2] = useState(false);
  const [showGuessedAcc, setShowGuessedAcc] = useState(false);
  const [isproviderSelected, setIsProviderSelected] = useState(null);
  const [savedBene, setSavedBene] = useState(false);
  const [flag, setFlag] = useState(require("../../assets/kenya.png"));
  const [country, setCountry] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const [currencyCode, setCurrencyCode] = useState("");
  const [channelID, setChannelID] = useState("");
  const [networkID, setNetworkID] = useState("");
  const [symbol, setSymbol] = useState("");
  const [serviceProvider, setServiceProvider] = useState([]);
  const [phone, setPhone] = useState("");
  const [fullName, setFullName] = useState("");
  const [nameError, setNameError] = useState(false);
  const activeFlagRef = useRef<any | null>(null);
  const countryRef = useRef<String | null>(null);
  const yellowCardRef = useRef<String | null>(null);
  const currencyCodeRef = useRef<String | null>(null);
  const symbolRef = useRef<String | null>(null);
  const mobileCodeRef = useRef<String | null>(null);
  const alphaCode2Ref = useRef<String | null>(null);
  const [aplhaCode2, setAlphaCode2] = useState("");
  const [yellowCard, setYellowCard] = useState("");
  const [mobileCode, setMobileCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [showNotes, setShowNotes] = useState(false);
  const [selectedNote, setSelectedNote] = useState("");
  const [noteError, setNoteError] = useState(false);
  const [show, setShow] = useState(false);
  const [accNum, setAccNum] = useState("");
  const [loading1, setLoading1] = useState(false);
  const [error, setError] = useState(false);
  const [error1, setError1] = useState("");
  const [accErr, setAccErr] = useState("");
  const [bankErr, setBankErr] = useState("");
  const [bene, setBene] = useState(false);
  const [details, setDetails] = useState<any>([]);
  const [isOn, setIsOn] = useState(false);
  const [loader, setLoader] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState<any>([]);
  const [showChannelError, setShowChannelError] = useState(false);
  const [gateWayError, setGateWayError] = useState(false);
  const [prError, setPrError] = useState("");

  const notes = [
    "Gift",
    "Bills",
    "Groceries",
    "Travel",
    "Health",
    "Entertainment",
    "Housing",
    "School/fees",
    "Other",
  ];
  const savedBeneficiary = [
    // {
    //   name: "John Doe",
    //   accNum: "**********",
    //   bankName: "SFx money app",
    // },
  ];
  const handleActiveCountry = (newActiveType: string | null) => {
    setCountry(newActiveType);
  };
  const handleActiveMobileCode = (newActiveType: string | null) => {
    setMobileCode(newActiveType);
  };
  const handleActiveFlag = (newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  };
  const handleActiveYellowCard = (newActiveType: string | null) => {
    setYellowCard(newActiveType);
  };
  const handleActiveCurrencyCode = (newActiveType: string | null) => {
    setCurrencyCode(newActiveType);
  };
  const handleActiveSymbol = (newActiveType: string | null) => {
    setSymbol(newActiveType);
  };
  const handleActiveAplhaCode2 = (newActiveType: string | null) => {
    setAlphaCode2(newActiveType);
  };
  useEffect(() => {
    countryRef.current = country;
  }, [country]);
  useEffect(() => {
    alphaCode2Ref.current = aplhaCode2;
  }, [aplhaCode2]);
  useEffect(() => {
    activeFlagRef.current = flag;
  }, [flag]);
  useEffect(() => {
    yellowCardRef.current = yellowCard;
  }, [yellowCard]);
  useEffect(() => {
    currencyCodeRef.current = currencyCode;
  }, [currencyCode]);
  useEffect(() => {
    symbolRef.current = symbol;
  }, [symbol]);
  useEffect(() => {
    mobileCodeRef.current = mobileCode;
  }, [mobileCode]);
  useEffect(() => {
    if (!country) {
      setFlag(require("../../assets/kenya.png"));
    }
  }, [country]);
  const getNetwork = async (code) => {
    // setLoading(true)
    try {
      const networks = await GetNetwork(code);
      networks.forEach((item) => {});
      return networks;
    } catch (error) {
      return [];
    }
  };

  const getChannels = async (code) => {
    setLoading(true);
    try {
      const channels = await withApiErrorToast(GetChannels(code), handleToast);
      if (channels.error) {
        setShowChannelError(true);
        setGateWayError(true);
      } else {
        setShowChannelError(false);
        setGateWayError(false);
      }
      const result = channels.filter(
        (item) => item.rampType === "withdraw" && item.channelType === "momo"
      );
      if (result.length > 0) {
        setChannelID(result[0].id);
        result.forEach((item) => {});
      }
      setLoading(false);
      return result; // Return the filtered channels
    } catch (error) {
      setLoading(false);
      return []; // Return an empty array in case of an error
    }finally{
      setLoading(false)
    }
  };
  // const getAccountName = async () => {
  //   if (
  //     phone != "" &&
  //     phone != null &&
  //     countryCode2 != "" &&
  //     countryCode2 != null
  //   ) {
  //     setLoading1(true);
  //     setError1("");
  //     try {
  //       const body = {
  //         accountNumber: phone,
  //         networkId: networkID,
  //       };
  //       const accountName = await GetAccountName(body);
  //       if (accountName.accountName) {
  //         setLoading1(false);
  //         setBene(true);
  //         setDetails(accountName);
  //         setError1("");
  //       } else {
  //         setLoading(false);
  //         setBene(false);
  //         setError1("Username not found.");
  //       }
  //     } catch (error) {
  //       ;
  //     }
  //   }
  // };
  // useEffect(() => {
  //   getAccountName();
  // }, [networkID, phone]);
  useEffect(() => {
    const fetchData = async () => {
      if (yellowCard) {
        const networks = await getNetwork(yellowCard);
        const channels = await getChannels(yellowCard);
        const ntw = networks
          .map((n: any) => ({
            ...n,
            matchingChannelCount: n.channelIds.filter(
              (id: string) => channels.some((channel) => channel.id === id) // Correctly match the id
            ).length,
          }))
          .filter((n: any) => n.matchingChannelCount > 0) // Only keep networks with matching channels
          .sort(
            (a: any, b: any) => b.matchingChannelCount - a.matchingChannelCount
          );
        setServiceProvider(ntw);
        if (ntw.length === 1) {
          setCountryCode2(ntw[0].name);
          setNetworkID(ntw[0].id);
        } else {
          setCountryCode2("");
          setNetworkID("");
        }
      }
    };
    fetchData();
  }, [yellowCard]);

  const validateInput = () => {
    let errorHandling = 1;
    if (phone.length < 7) {
      setError(true);
      errorHandling = 0;
    } else {
      setError(false);
    }
    if (countryCode2 === "") {
      setPrError("Select your service provider");
      errorHandling = 0;
    } else {
      setPrError("");
    }
    if (selectedNote.length === 0) {
      setNoteError(true);
      errorHandling = 0;
    } else {
      setNoteError(false);
    }
    if (fullName.length === 0) {
      setNameError(true);
      errorHandling = 0;
    } else {
      setNameError(false);
    }
    if (errorHandling === 1) {
      navigation.navigate("AmountScreen1", {
        country: country,
        channelID: channelID,
        currencyCode: currencyCode,
        symbol: symbol,
        networkID: networkID,
        provider: countryCode2,
        phone: `${mobileCode}${phone?.trim().replace(/^0/, "")}`,
        accName: fullName,
        note: selectedNote,
        aplhaCode2: aplhaCode2,
      });
    } else {
    }
  };
  const getBeneficiaries = async () => {
    try {
      const response = await withApiErrorToast(GetBeneficiaries(1, 10, "mobile-money"), handleToast);
      if (response.items) {
        setLoader(false);
        setBeneficiaries(response?.items);
      } else {
        setLoader(false);
        handleToast(response.message, "error");
      }
    } catch (error) {}finally{
      setLoader(false)
    }
  };

  const AddBene = async () => {
    try {
      const body = {
        account: `${mobileCode}${phone?.trim().replace(/^0/, "")}`,
        providerName: countryCode2,
        name: fullName,
        type: "mobile-money",
        country: yellowCard,
        yellowCardChannelId: channelID,
        yellowCardNetworkId: networkID,
        isManualNetworkInput: false, // Mobile money typically doesn't use manual network input
      };

      const response = await withApiErrorToast(AddBeneficiary(body), handleToast);
      if (response.account) {
        handleToast("Beneficiary saved", "success");
        getBeneficiaries();
      } else {
        handleToast(response.message, "error");
        setIsOn(false);
      }
    } catch (error) {}
  };
  const getCurrencyCode = (yc) => {
    if (yc === "TR" || yc === "TR") {
      return "TRY";
    }
    const country = countries.find((item) => item.YellowCardCode === yc);

    return country ? country.currencyCode : "Country not found";
  };

  const getSymbol = (currencyCode) => {
    if (currencyCode === "TR") {
      return "₺";
    }
    const curSymbol = countries.find(
      (item) => item.YellowCardCode === currencyCode
    );
    // setSymbol(curSymbol)
    return curSymbol ? curSymbol.symbol : "Symbol not found";
  };
  const handleSwitch = () => {
    if (isOn) {
      setIsOn(false);
    } else {
      setIsOn(true);
      let errorHandling = 1;
      if (phone.length < 7) {
        setError(true);
        errorHandling = 0;
      } else {
        setError(false);
      }
      if (countryCode2 === "") {
        setPrError("Select your service provider");
        errorHandling = 0;
      } else {
        setPrError("");
      }
      if (fullName.length === 0) {
        setNameError(true);
        errorHandling = 0;
      } else {
        setNameError(false);
      }
      if (selectedNote.length === 0) {
        setNoteError(true);
        errorHandling = 0;
      } else {
        setNoteError(false);
      }
      if (errorHandling === 1) {
        AddBene();
      } else {
        handleToast("Error saving beneficiary", "error");
        setIsOn(false);
      }
    }
  };

  useFocusEffect(
    useCallback(() => {
      setLoader(true);
      getBeneficiaries();
    }, [])
  );
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Mobile money" navigation={navigation} />
        <ScrollView
          contentContainerStyle={{ paddingBottom: "20%" }}
          automaticallyAdjustKeyboardInsets={true}
        >
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <TouchableOpacity
                onPress={() => {
                  setShowCountries(true);
                }}
              >
                <Input
                  value={country}
                  label="Country"
                  placeholder="Kenya"
                  inputStyle={{ width: "65%", color: "#161817" }}
                  // contStyle={{ marginTop: 16 }}
                  editable={false}
                  leftIcon={
                    <Image
                      source={flag}
                      style={{
                        width: 24,
                        height: 24,
                        marginLeft: 14,
                        objectFit: aplhaCode2 === "NG" ? "fill" : "cover",
                        borderRadius: 100,
                      }}
                    />
                  }
                  rightIcon={
                    <View
                      style={{
                        //   backgroundColor: "red",
                        width: "15%",
                        height: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <SvgXml xml={svg.dropDown} />
                    </View>
                  }
                />
              </TouchableOpacity>
              {serviceProvider.length > 0 && (
                <>
                  <TouchableOpacity
                    onPress={() => {
                      setShow2(true);
                    }}
                    disabled={serviceProvider.length === 1 ? true : false}
                  >
                    <Input
                      value={countryCode2}
                      label="Service provider"
                      placeholder="Momo"
                      inputStyle={{ width: "65%", color: "#161817" }}
                      editable={false}
                      error={prError === "" ? false : true}
                      customInputStyle={{
                        backgroundColor:
                          serviceProvider.length === 1
                            ? colors.secBackground
                            : "transparent",
                        borderWidth: serviceProvider.length === 1 ? 0 : 1,
                      }}
                      contStyle={{
                        marginTop: 16,
                      }}
                      leftIcon={
                        <View>
                          <Image
                            source={moneyIcon}
                            style={{ width: 24, height: 24, marginLeft: 14 }}
                          />
                        </View>
                      }
                      rightIcon={
                        serviceProvider.length === 1 ? (
                          <></>
                        ) : (
                          <View
                            style={{
                              //   backgroundColor: "red",
                              width: "15%",
                              height: "100%",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <SvgXml xml={svg.dropDown} />
                          </View>
                        )
                      }
                    />
                  </TouchableOpacity>
                  {prError && <P style={styles.errorText}>{prError}</P>}
                </>
              )}

              {country != "" && (
                <>
                  <Input
                    label={`Mobile number `}
                    placeholder="**********"
                    inputStyle={{ width: "90%" }}
                    contStyle={{ marginTop: 16 }}
                    value={phone}
                    onChangeText={(e) => {
                      // Remove all white spaces and non-numeric characters
                      const cleanedText = e.replace(/\s/g, '').replace(/[^0-9]/g, '');
                      setPhone(cleanedText);
                      if (cleanedText.length > 7) {
                        setError(false);
                      }
                    }}
                    keyboardType={"numeric"}
                    error={error}
                    leftIcon={
                      <TouchableOpacity
                        style={[styles.pinInput]}
                        onPress={() => {
                          setShow(true);
                        }}
                      >
                        <Image
                          source={flag}
                          style={{
                            width: 24,
                            height: 24,
                            marginLeft: (14 / baseWidth) * width,
                            marginRight: (10 / baseWidth) * width,
                            borderRadius: 100,
                            objectFit: aplhaCode2 === "NG" ? "fill" : "cover",
                          }}
                        />
                        {/* @ts-ignore */}
                        <P style={[styles.pinTextInput, { fontSize: 12 }]}>
                          {mobileCode}
                        </P>
                      </TouchableOpacity>
                    }
                  />
                  {error && (
                    <P style={styles.errorText}>
                      Number should not be less than 7 digit
                    </P>
                  )}
                  {error1 ? (
                    <View style={{ width: "100%" }}>
                      <P
                        style={{
                          color: colors.red,
                          marginTop: 8,
                          textAlign: "left",
                          fontSize: 12,
                        }}
                      >
                        {error1}
                      </P>
                    </View>
                  ) : null}
                  {/* {loading1 ? (
                    <ContentLoader
                      style={{ marginBottom: 16, marginTop: 16 }}
                      width={"100%"}
                      height={44}
                      speed={2}
                      backgroundColor="#F7F4FF"
                      foregroundColor="#ecebeb"
                    >
                      <Rect
                        x="0"
                        y="0"
                        rx="4"
                        ry="4"
                        width="100%"
                        height="44"
                      />
                    </ContentLoader>
                  ) : (
                    bene && (
                      <View style={styles.benefeciary}>

                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >

                          <P>{`${details?.accountName}`}</P>
                        </View>
                        <SvgXml xml={svg.green_check} />
                      </View>
                    )
                  )} */}
                  <Input
                    label={`Account name`}
                    placeholder="John Doe"
                    inputStyle={{ width: "80%" }}
                    contStyle={{ marginTop: 16 }}
                    value={fullName}
                    onChangeText={(e) => {
                      setFullName(e);
                      setNameError(false);
                    }}
                    error={nameError}
                  />

                  {nameError && (
                    <P style={styles.errorText}>Account name is required</P>
                  )}
                </>
              )}
              {country != "" && (
                <TouchableOpacity
                  onPress={() => {
                    setShowNotes(true);
                  }}
                >
                  <Input
                    label={<P style={styles.label}>Note {/* @ts-ignore */}</P>}
                    contStyle={{ marginTop: 16 }}
                    placeholder="Enter note here..."
                    editable={false}
                    value={selectedNote}
                    error={noteError}
                    rightIcon={
                      <View
                        style={{
                          //   backgroundColor: "red",
                          width: "15%",
                          height: "100%",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <SvgXml xml={svg.dropDown} />
                      </View>
                    }
                    // keyboardType="numeric"
                    // contStyle={{ marginTop: 16 }}
                  />
                  {noteError && (
                    <P style={styles.errorText}>Note is required</P>
                  )}
                </TouchableOpacity>
              )}

              <View style={styles.switchCase}>
                <CustomSwitch1 isOn={isOn} onToggle={handleSwitch} />
                <P
                  style={{
                    marginLeft: 4,
                    fontSize: 12,
                    color: colors.gray,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  Save Baneficiary
                </P>
              </View>
            </View>
            <View style={{ width: "80%", alignSelf: "center", marginTop: 32 }}>
              <Button
                btnText="Next"
                onPress={() => {
                  if (gateWayError) {
                    setShowChannelError(true);
                  } else {
                    validateInput();
                  }
                }}
                disabled={country === "" || serviceProvider.length === 0}
              />
            </View>
            <View style={styles.beneficiaryCont}>
              <View style={styles.beneNav}>
                <P style={{ color: colors.gray, fontSize: 12, lineHeight: 18 }}>
                  Beneficiaries
                </P>
                {beneficiaries.length > 0 && (
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    <TouchableOpacity
                      onPress={() =>
                        navigation.navigate("Beneficiaries", {
                          actT: "mobile-money",
                        })
                      }
                    >
                      <P
                        style={{
                          color: "#8B52FF",
                          textDecorationLine: "underline",
                          alignItems: "center",
                          fontSize: 12,
                        }}
                      >
                        View all
                      </P>
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {beneficiaries.length === 0 ? (
                <View style={styles.emptyCont}>
                  <SvgXml xml={svg.userGroup} />
                  <P
                    style={{
                      fontFamily: fonts.poppinsMedium,
                      lineHeight: 21,
                      marginTop: 16,
                      fontSize: 12,
                    }}
                  >
                    No beneficiary!
                  </P>
                  <P
                    style={{
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                      color: colors.gray2,
                    }}
                  >
                    You have no beneficiary yet
                  </P>
                </View>
              ) : (
                <>
                  {beneficiaries.slice(0, 4).map((item, index) => {
                    return (
                      <TouchableOpacity
                        key={index}
                        onPress={() => {
                          navigation.navigate("AmountScreen1", {
                            country: item?.country,
                            channelID: item?.yellowCardChannelId,
                            currencyCode: getCurrencyCode(item?.country),
                            symbol: getSymbol(item?.country),
                            networkID: item?.yellowCardNetworkId,
                            provider: item?.providerName,
                            phone: item?.account?.trim(),
                            accName: item?.name,
                            note: "Other",
                            aplhaCode2: item?.country,
                          });
                        }}
                      >
                        <BenediciaryComponent
                          svg={svg.mobile}
                          text1={item.name}
                          text2={`${item.account} | ${item.providerName}`}
                        />
                      </TouchableOpacity>
                    );
                  })}
                </>
              )}
            </View>
          </View>
        </ScrollView>
      </Div>
      {loader && <Loader />}
      {loading && <Loader />}
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Select country"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: "75%" }}
        extraModalStyle={{ height: "73%" }}
        componentHolderStyle={{ flex: 1 }}
        components={
          <CountrySelect
            excludedCountries={NotMMPauOutCountries}
            onActiveCountryChange={handleActiveCountry}
            onActiveFlag={handleActiveFlag}
            onActiveYellowCard={handleActiveYellowCard}
            onActiveCurrencyCode={handleActiveCurrencyCode}
            onSymbolChange={handleActiveSymbol}
            onActiveMobileCodeChange={handleActiveMobileCode}
            onActiveAlphaCode2Change={handleActiveAplhaCode2}
            onPress={(index: number) => {
              console.log("Country selected with index:", index);
              // Important: Close the modal AFTER all the callbacks have been processed
              setTimeout(() => {
                setLoading(true);
                setShowCountries(false);
                setPhone("");
                setAccNum("");
                setSelectedNote("");
                setFullName("");
              }, 100);
            }}
          />
        }
      />
      <BottomSheet
        isVisible={showNotes}
        showBackArrow={false}
        // backspaceText="Select country"
        onClose={() => setShowNotes(false)}
        modalContentStyle={{ height: "70%" }}
        extraModalStyle={{ height: "68%" }}
        components={
          <ScrollView
            contentContainerStyle={{ paddingBottom: 200 }}
            showsVerticalScrollIndicator={false}
          >
            <View style={{ paddingTop: 16 }}>
              {notes.length === 0 ? (
                <>
                  <P>No available note</P>
                </>
              ) : (
                <>
                  {notes.map((item, index) => {
                    return (
                      <ListItemSelect
                        key={index}
                        text1={item}
                        onPress={() => {
                          setSelectedNote(item);
                          setShowNotes(false);
                          setNoteError(false);
                        }}
                        containerStyle={{
                          marginBottom: 16,
                          marginTop: index == 0 ? 6 : 0,
                        }}
                        isActive={selectedNote == item}
                      />
                    );
                  })}
                </>
              )}
            </View>
          </ScrollView>
        }
      />
      <BottomSheet
        isVisible={show2}
        onClose={() => setShow2(false)}
        backspaceText="Provider"
        showBackArrow={false}
        modalContentStyle={{ height: "70%" }}
        extraModalStyle={{ height: "68%" }}
        components={
          <View>
            <P
              style={{
                marginTop: 24,
                fontSize: 12,
                lineHeight: 19.2,
                color: colors.gray,
              }}
            >
              Select your service provider
            </P>
            <View>
              {serviceProvider.map((item, index) => {
                return (
                  <ListItemSelect
                    key={index}
                    text1={item.name}
                    image={require("../../assets/momo.png")}
                    onPress={() => {
                      setIsProviderSelected(index);
                      setCountryCode2(item.name);
                      setNetworkID(item.id);
                      setShow2(false);
                      setPrError("");
                    }}
                    containerStyle={{
                      marginBottom: 16,
                      marginTop: index == 0 ? 6 : 0,
                    }}
                    isActive={isproviderSelected == index}
                  />
                );
              })}
            </View>
          </View>
        }
      />
      <BottomSheet
        isVisible={showChannelError}
        showBackArrow={false}
        backspaceText=""
        onClose={() => setShowChannelError(false)}
        modalContentStyle={{ height: "55%" }}
        extraModalStyle={{ height: "53%" }}
        components={
          <View
            style={{
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
              paddingTop: 34,
            }}
          >
            <SvgXml xml={svg.noCloud} width={44} height={44} />

            <P style={{ marginTop: 16 }}>Withdrawal option unavailable!</P>
            <P
              style={{
                textAlign: "center",
                fontSize: 12,
                color: colors.gray,
                marginTop: 4,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              We apologize for any inconvenience this may cause and appreciate
              your patience while we work to enhance our payment options.
            </P>

            <View style={{ marginTop: 32 }}>
              <Button
                btnText="Explore other payment options"
                onPress={() => {
                  setShowChannelError(false);
                  navigation.pop();
                }}
              />
            </View>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: "100%",
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  pyItem: {
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: "center",
    flexDirection: "row",
    padding: 16,
  },
  pyName: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  rate: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  noteCont: {
    width: "100%",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
  },
  label: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
  switchCase: {
    width: "100%",
    // backgroundColor: "red",
    alignItems: "flex-start",
    flexDirection: "row",
    marginTop: 16,
  },
  beneficiaryCont: {
    width: "90%",
    backgroundColor: colors.white,
    borderRadius: 12,
    alignSelf: "center",
    marginTop: 60,
    minHeight: 246,
    paddingBottom: 20,
  },
  beneNav: {
    width: "100%",
    paddingTop: 11,
    paddingLeft: 24,
    paddingRight: 16,
    paddingBottom: 13,
    flexDirection: "row",
    justifyContent: "space-between",
    borderBottomWidth: 1,
    borderColor: colors.stroke,
  },
  emptyCont: {
    width: "100%",
    height: 204,
    alignItems: "center",
    justifyContent: "center",
  },
  errorText: {
    color: colors.red,
    fontSize: 10,
    marginTop: 5,
    fontFamily: fonts.poppinsRegular,
    // marginTop: -30,
  },
  pinInput: {
    width: "35%",
    height: "100%",
    alignItems: "center",
    flexDirection: "row",
    borderRightColor: "#E6E5E5",
    borderRightWidth: 1,
  },
  benefeciary: {
    paddingTop: 4,
    paddingRight: 24,
    paddingBottom: 4,
    paddingLeft: 24,
    backgroundColor: "#F7F4FF",
    borderRadius: 8,
    flexDirection: "row",
    width: "100%",
    height: 44,
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 16,
  },
});
