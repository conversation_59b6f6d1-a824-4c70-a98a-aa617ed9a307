import React, { useState, useEffect } from "react";
import { View, StyleSheet, Dimensions, ScrollView, Image } from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import InputCard from "../../components/InputCard";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { colors } from "../../config/colors";
import {
  GetMinMAx,
  GetRateByCountry,
  GetRateById,
  GetUserWallet,
} from "../../RequestHandlers/Wallet";
import { GetYellowCardEstimatedFee } from "../../RequestHandlers/Wallet";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import useDebounce from "../../components/Debounce";
import { useToast } from "../../context/ToastContext";
import BottomSheet from "../../components/BottomSheet";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");

export default function AmountScreen1({ navigation, route }) {
  const { handleToast } = useToast();
  const [inputValue, setInputValue] = useState("0");
  const [error, setError] = useState(false);
  const [isUsdInput, setIsUsdInput] = useState(true); // Track which currency is being input
  const [ngnRate, setNgnRate] = useState(0);
  const [bal, setBal] = useState(0);
  const [loader, setLoader] = useState(false);
  const [localAmount, setLocalAmount] = useState(0.0);
  const [localFee, setLocalFee] = useState(0);
  const [fee, setFee] = useState<any>([]);
  const [feeInUSD, setFeeInUSD] = useState(0);
  const [mainAmountInLocal, setMainAmountInLocal] = useState(0);
  const debouncedValue = useDebounce(Number(inputValue), 500);
  const debouncedValue2 = useDebounce(Number(inputValue) / ngnRate, 300);
  const [showChannelError, setShowChannelError] = useState(false);
  const [gateWayError, setGateWayError] = useState(false);
  const [min, setMin] = useState(0);
  const [max, setMax] = useState(0);
  const [balLoading, setBalLoading] = useState(false);
  const {
    currencyCode,
    symbol,
    networkID,
    channelID,
    country,
    provider,
    phone,
    accName,
    note,
    aplhaCode2,
  } = route?.params || "";
  const formatNumber = (value) => {
    value = value?.toString();
    return value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const handleKeyPress = (key) => {
    setError(false);

    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
    } else {
      setInputValue((prev) => {
        let newValue = prev === "0" && key !== "." ? key : prev + key;
        newValue = newValue.replace(/[^0-9.]/g, ""); // Remove any non-numeric characters
        return newValue;
      });
    }
  };

  const toggleCurrency = () => {
    setIsUsdInput(!isUsdInput);
    setInputValue("0");
  };

  const getWallet = async () => {
    setBalLoading(true);
    try {
      const bal = await GetUserWallet();
      setBal(bal.totalInUsd);
    } catch (error) {
    } finally {
      setBalLoading(false);
    }
  };

  const getRateById = async () => {
    try {
      const rate = await GetRateById(currencyCode);
      const sfxRate = await GetRateByCountry(currencyCode, "yellow-card");
      sfxRate.map((item, index) => {
        if (item.type === "sell") {
          setNgnRate(item.amount);
        }
      });
    } catch (error) {}
  };

  const getFee = async () => {
    try {
      const res = await GetYellowCardEstimatedFee(
        aplhaCode2,
        debouncedValue,
        "momo",
        "WITHDRAW",
        isUsdInput ? "USD" : currencyCode
      );
      if (res.error) {
        setGateWayError(true);
      } else {
        setGateWayError(false);
        setFee(res);
        if (Number(inputValue) === 0) {
          setLocalAmount(0.0);
          setLocalFee(0);
          setFeeInUSD(0);
          setMainAmountInLocal(0);
        } else {
          setLocalAmount(res.totalAmountInLocal);
          setLocalFee(res.feeInLocal);
          setFeeInUSD(res.fee);
          setMainAmountInLocal(res.localAmount);
        }
      }
    } catch (error) {}
  };
  const getMinMax = async (code) => {
    try {
      const res = await GetMinMAx(code, "momo", "WITHDRAW");
      if (res.maximumAmountInLocal) {
        setMin(res.minimumAmountInLocal);
        setMax(res.maximumAmountInLocal);
      }
    } catch (error) {}
  };
  useEffect(() => {
    getFee();
  }, [debouncedValue]);

  useEffect(() => {
    getRateById();
    getWallet();
  }, []);
  useEffect(() => {
    getFee();
  }, [isUsdInput ? debouncedValue : debouncedValue2]);
  useEffect(() => {
    if (aplhaCode2) {
      getMinMax(aplhaCode2);
    }
  }, [aplhaCode2]);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Amount" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <View
                style={{
                  alignItems: "center",
                  marginBottom: 16,
                }}
              >
                <Image
                  source={require("../../assets/momo.png")}
                  style={{
                    width: 32,
                    height: 32,
                    marginBottom: 8,
                  }}
                />
                <P style={{ fontSize: 12 }}>{accName}</P>
                <P
                  style={{
                    fontSize: 12,
                    color: colors.gray,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  {phone} | {provider}
                </P>
              </View>
              <InputCard
                headerText="How much do you want to send"
                onTogglePress={toggleCurrency}
                toggleStyle={{ top: "40%" }}
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 32,
                        lineHeight: 48,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `$${formatNumber(inputValue)}`
                        : `${symbol}${formatNumber(inputValue)}`}
                      <P style={{ lineHeight: 48 }}>
                        {isUsdInput ? "USD" : currencyCode}
                      </P>
                    </P>
                  </>
                }
                convertedValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 16,
                        lineHeight: 24,
                        marginRight: 4,
                      }}
                    >
                      {isUsdInput
                        ? `${symbol}${formatToTwoDecimals(
                            Number(inputValue) * ngnRate
                          )}`
                        : `$${formatToTwoDecimals(
                            Number(inputValue) / ngnRate
                          )}`}
                      <P style={{ lineHeight: 24, fontSize: 12 }}>
                        {isUsdInput ? currencyCode : "USD"}
                      </P>
                    </P>
                  </>
                }
                extraComponent1={
                  <>
                    <View style={[styles.fee, { marginTop: 8 }]}>
                      <View style={{ width: 24, alignItems: "center" }}>
                        <SvgXml xml={svg.coin1} style={{ marginRight: 8 }} />
                      </View>
                      <P style={{ fontSize: 11, color: colors.gray }}>
                        Charges: {formatToTwoDecimals(feeInUSD)} USD
                      </P>
                    </View>
                    <View style={styles.fee}>
                      <View style={{ width: 24, alignItems: "center" }}>
                        <SvgXml
                          xml={svg.watterFall}
                          style={{ marginRight: 8 }}
                        />
                      </View>
                      <P style={{ fontSize: 11, color: colors.gray }}>
                        Exchange rate: 1 USD ~ {ngnRate} {currencyCode}
                      </P>
                    </View>
                  </>
                }
                text2={`Available balance: ${
                  bal && !balLoading ? `$${formatToTwoDecimals(bal)}` : ""
                }`}
                error={error}
                loading={balLoading}
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
              <View
                style={{
                  width: "80%",
                  alignSelf: "center",
                  marginTop: (16 / baseHeight) * height,
                }}
              >
                <Button
                  btnText="Next"
                  loading={loader}
                  onPress={() => {
                    const inputAsNumber = Number(inputValue);

                    // Input converted to USD (even if user entered local)
                    const inputInUSD = isUsdInput
                      ? inputAsNumber
                      : inputAsNumber / ngnRate;

                    // Fee is assumed to already be in USD (feeInUSD)
                    const totalAmountInUSD = inputInUSD + feeInUSD;

                    // Insufficient balance check
                    const isInsufficientBalance = totalAmountInUSD > bal;

                    // Minimum amount from API, but at least $1
                    const apiMinUSD = min / ngnRate;
                    const effectiveMinUSD = apiMinUSD < 3 ? 3 : apiMinUSD;
                    const effectiveMinLocal = effectiveMinUSD * ngnRate;

                    // Maximum amount from API, but no more than $3000
                    const apiMaxUSD = max / ngnRate;
                    const effectiveMaxUSD = apiMaxUSD > 3000 ? 3000 : apiMaxUSD;
                    const effectiveMaxLocal = effectiveMaxUSD * ngnRate;

                    // Min/Max checks
                    const isAmountTooSmall = isUsdInput
                      ? inputAsNumber < effectiveMinUSD
                      : inputAsNumber < effectiveMinLocal;

                    const isAmountTooLarge = isUsdInput
                      ? inputAsNumber > effectiveMaxUSD
                      : inputAsNumber > effectiveMaxLocal;

                    if (inputValue === "0") {
                      setError(true);
                      return;
                    }

                    if (isAmountTooSmall) {
                      setError(true);
                      handleToast(
                        `Minimum withdrawal amount is ${
                          isUsdInput
                            ? `$${formatToTwoDecimals(effectiveMinUSD)} USD`
                            : `${formatToTwoDecimals(
                                effectiveMinLocal
                              )} ${currencyCode}`
                        }`,
                        "error"
                      );
                      return;
                    }

                    if (isAmountTooLarge) {
                      setError(true);
                      handleToast(
                        `Maximum withdrawal amount is ${
                          isUsdInput
                            ? `$${formatToTwoDecimals(effectiveMaxUSD)} USD`
                            : `${formatToTwoDecimals(
                                effectiveMaxLocal
                              )} ${currencyCode}`
                        }`,
                        "error"
                      );
                      return;
                    }

                    if (isInsufficientBalance) {
                      setError(true);
                      handleToast("Insufficient balance", "error");
                      return;
                    }

                    if (gateWayError) {
                      setShowChannelError(true);
                      return;
                    }

                    // All validations passed
                    navigation.navigate("ConfirmDetailScreen1", {
                      country,
                      inputValue: isUsdInput
                        ? inputAsNumber
                        : inputAsNumber / ngnRate,
                      ngnRate,
                      currencyCode,
                      channelID,
                      networkID,
                      symbol,
                      provider,
                      phone,
                      accName,
                      note,
                      fee,
                      isUsdInput,
                      aplhaCode2,
                    });
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      <BottomSheet
        isVisible={showChannelError}
        showBackArrow={false}
        backspaceText=""
        onClose={() => setShowChannelError(false)}
        modalContentStyle={{ height: "55%" }}
        extraModalStyle={{ height: "53%" }}
        components={
          <View
            style={{
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
              paddingTop: 34,
            }}
          >
            <SvgXml xml={svg.noCloud} width={44} height={44} />

            <P style={{ marginTop: 16 }}>Withdrawal option unavailable!</P>
            <P
              style={{
                textAlign: "center",
                fontSize: 12,
                color: colors.gray,
                marginTop: 4,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              We apologize for any inconvenience this may cause and appreciate
              your patience while we work to enhance our payment options.
            </P>

            <View style={{ marginTop: 32 }}>
              <Button
                btnText="Explore other payment options"
                onPress={() => {
                  setShowChannelError(false);
                  navigation.pop();
                }}
              />
            </View>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    // paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
  },
  bottom: {
    width,
    flex: 1,
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.1),
    marginTop: 20,
  },
  fee: {
    flexDirection: "row",
    alignItems: "center",
    // width: "100%"
    padding: 4,
    paddingLeft: 0,
  },
});
