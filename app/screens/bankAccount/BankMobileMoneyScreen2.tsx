import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Switch,
  Text,
  TextInput,
  Modal,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { svg } from "../../config/Svg";
import { SvgXml } from "react-native-svg";
import NoteComponent from "../../components/NoteComponent";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import CountrySelect from "../../components/CountrySelect";
import Button from "../../components/Button";
import ListItemSelect from "../../components/ListItemSelect";
import ListItemSelect2 from "../../components/ListItemSelect2";
import BenediciaryComponent from "../../components/BeneficiaryComponent";
import CustomSwitch from "../../components/CustomSwitch";
import NoteComponent2 from "../../components/NoteComponent2";
import BarCodeScanner from "../../components/BarCodeScanner";
import {
  GetAccountName,
  GetChannels,
  LinkGetAccountName,
  GetManualNetwork,
} from "../../RequestHandlers/Wallet";
import {
  NotBaCountires,
  NotBaOUTCountires,
} from "../../components/NotBaCountirs";
import Loader from "../../components/ActivityIndicator";
import { GetNetwork } from "../../RequestHandlers/Wallet";
import ContentLoader, { Rect } from "react-content-loader/native";
import CustomSwitch1 from "../../components/CustomSwitch1";
import { AddBeneficiary, CheckId } from "../../RequestHandlers/User";
import { GetBeneficiaries } from "../../RequestHandlers/User";
import { useFocusEffect } from "@react-navigation/native";
import { countries } from "../../components/counties";
import { GetLinkBanks } from "../../RequestHandlers/Wallet";
import { TestBanks } from "../../components/TestBanks";
import ExtraId from "../../components/ExtraId";
import { GetRateById } from "../../RequestHandlers/Wallet";
import { GetRateByCountry } from "../../RequestHandlers/Wallet";
import useDebounce from "../../components/Debounce";
import { securityQuestions } from "../../components/securityQuestions";
import { formatNumber } from "../../Utils/formatNumber";
import { useToast } from "../../context/ToastContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const { width, height, fontScale } = Dimensions.get("window");

export default function BankMobileMoneyScreen2({ navigation }) {
  const [countryCode2, setCountryCode2] = useState("");
  const [moneyIcon, setMoneyIcon] = useState(require("../../assets/sfx1.png"));
  const [flag, setFlag] = useState(require("../../assets/turkey.png"));
  const [country, setCountry] = useState("");
  const [showCountries, setShowCountries] = useState(false);
  const [show2, setShow2] = useState(false);
  const [showGuessedAcc, setShowGuessedAcc] = useState(false);
  const [isproviderSelected, setIsProviderSelected] = useState(null);
  const [savedBene, setSavedBene] = useState(false);
  const [bene, setBene] = useState(false);
  const [showQrCode, setShowQrCode] = useState(false);
  const activeFlagRef = useRef<any | null>(null);
  const countryRef = useRef<String | null>(null);
  const yellowCardRef = useRef<String | null>(null);
  const currencyCodeRef = useRef<String | null>(null);
  const symbolRef = useRef<String | null>(null);
  const alphaCode2Ref = useRef<String | null>(null);
  const [aplhaCode2, setAlphaCode2] = useState("");
  const [yellowCard, setYellowCard] = useState("");
  const [currencyCode, setCurrencyCode] = useState("");
  const [networkID, setNetworkID] = useState("");
  const [symbol, setSymbol] = useState("");
  const [loading, setLoading] = useState(false);
  const [serviceProvider, setServiceProvider] = useState([]);
  const [channelID, setChannelID] = useState("");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [accNum, setAccNum] = useState("");
  const [accName, setAccName] = useState("");
  const [loading1, setLoading1] = useState(false);
  const [details, setDetails] = useState<any>([]);
  const [showNotes, setShowNotes] = useState(false);
  const [selectedNote, setSelectedNote] = useState("");
  const [noteError, setNoteError] = useState("");
  const [error, setError] = useState("");
  const [accErr, setAccErr] = useState("");
  const [bankErr, setBankErr] = useState("");
  const [isOn, setIsOn] = useState(false);
  const [loader, setLoader] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState<any>([]);
  const [banks, setBanks] = useState<any>([]);
  const [bankCode, setBankCode] = useState("");
  const [showMultiOption, setShowMultiOption] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [NextState, setNextState] = useState(null);
  const [showReq, setShowReq] = useState(false);
  const [showD, setShowD] = useState(false);
  const [gateWayError, setGateWayError] = useState(true);
  const [showChannelError, setShowChannelError] = useState(false);
  const [bankImg, setBankImg] = useState(
    "https://res.cloudinary.com/dqw0lwkil/image/upload/v1675739938/LINK/Bank_List/palmpaybank_ayyotp.png"
  );
  const [accNameErr, setAccNameErr] = useState("");
  const debouncedValue = useDebounce(accNum, 500);
  const [TypedBank, setTypedBank] = useState("");
  const [isManualMode, setIsManualMode] = useState(false);
  const [manualNetworkId, setManualNetworkId] = useState("");
  const { handleToast } = useToast();
  const notes = [
    "Gift",
    "Bills",
    "Groceries",
    "Travel",
    "Health",
    "Entertainment",
    "Housing",
    "School/fees",
    "Other",
  ];
  const [options, setOptions] = useState([
    {
      time: "5 min",
      rate: "₦1,600",
      minMax: "$3 - $3,000",
      requirement: "NIN and BVN",
      type: "yellowCard",
    },
    {
      time: "15 min",
      rate: "₦1,600",
      minMax: "$15 - $3,000",
      requirement: "BVN or passport",
      type: "link",
    },
  ]);
  const filteredBank = searchQuery
    ? serviceProvider.filter((bank) =>
        bank.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : serviceProvider;
  const handleActiveCountry = (newActiveType: string | null) => {
    setCountry(newActiveType);
  };
  const handleActiveFlag = (newActiveType: any | null) => {
    if (newActiveType) {
      setFlag(newActiveType);
    }
  };
  const handleActiveYellowCard = (newActiveType: string | null) => {
    setYellowCard(newActiveType);
  };
  const handleActiveCurrencyCode = (newActiveType: string | null) => {
    setCurrencyCode(newActiveType);
  };
  const handleActiveSymbol = (newActiveType: string | null) => {
    setSymbol(newActiveType);
  };
  const handleActiveAplhaCode2 = (newActiveType: string | null) => {
    setAlphaCode2(newActiveType);
    setShowD(false);
    if (newActiveType === "NG") {
      setShowMultiOption(true);
      setSelectedOption(null);
    } else {
      setShowMultiOption(false);
    }
  };
  useEffect(() => {
    countryRef.current = country;
  }, [country]);
  useEffect(() => {
    alphaCode2Ref.current = aplhaCode2;
  }, [aplhaCode2]);
  useEffect(() => {
    yellowCardRef.current = yellowCard;
  }, [yellowCard]);
  useEffect(() => {
    currencyCodeRef.current = currencyCode;
  }, [currencyCode]);
  useEffect(() => {
    symbolRef.current = symbol;
  }, [symbol]);
  useEffect(() => {
    activeFlagRef.current = flag;
  }, [flag]);

  useEffect(() => {
    if (!country) {
      setFlag(require("../../assets/nigeria.png"));
    }
  }, [country]);

  const getNetwork = async (code) => {
    try {
      const networks = await GetNetwork(code);
      networks.forEach((item) => {});
      return networks;
    } catch (error) {
      return [];
    }
  };

  const getManualNetworkId = async (channelId) => {
    try {
      const allNetworks = await GetManualNetwork();
      const manual = allNetworks.filter((network) =>
        network.channelIds.includes(channelId)
      );
      if (manual.length > 0) {
        return manual[0].id; // Return the first matching network ID
      }
      return null;
    } catch (error) {
      return null;
    }
  };

  const fetchRates = async () => {
    try {
      const yellowCardRates = await GetRateByCountry("NGN", "yellow-card");
      const linkRates = await GetRateByCountry("NGN", "link");
      // Process rates for yellow-card
      const yellowCardOption = yellowCardRates
        .filter((item) => item.type === "sell")
        .map((item) => ({
          time: "5 min",
          rate: `₦${formatNumber(item?.amount?.toFixed(2))}`,
          minMax: "$3 - $3,000",
          requirement: "NIN and BVN",
          type: "yellowCard",
        }))[0];
      const linkOption = linkRates
        .filter((item) => item.type === "sell")
        .map((item) => ({
          time: "15 min",
          rate: `₦${formatNumber(item?.amount?.toFixed(2))}`,
          minMax: "$20 - $3,000",
          requirement: "BVN or passport",
          type: "link",
        }))[0];
      setOptions([yellowCardOption, linkOption]);
    } catch (error) {}
  };
  useEffect(() => {
    fetchRates();
  }, []);

  const getChannels = async (code) => {
    setLoading(true);
    try {
      const channels = await withApiErrorToast(GetChannels(code), handleToast);
      if (channels.error) {
        setGateWayError(true);
      } else {
        setGateWayError(false);
      }
      const result = channels.filter(
        (item) =>
          (item.rampType === "withdraw" && item.channelType === "bank") ||
          item.channelType === "p2p" ||
          item.channelType === "eft"
      );
      if (result.length > 0) {
        setChannelID(result[0].id);
        result.forEach((item) => {});
      }
      setLoading(false);
      return result;
    } catch (error) {
      setLoading(false);
      return [];
    }finally{
      setLoading(false)
    }
  };

  const getAccountName = async () => {
    if (
      accNum != "" &&
      accNum != null &&
      countryCode2 != "" &&
      countryCode2 != null
    ) {
      setLoading1(true);
      setError("");
      try {
        if (country === "Nigeria" && selectedOption === "link") {
          const body = {
            account_number: accNum,
            bank_code: bankCode,
          };
          const accountName = await withApiErrorToast(LinkGetAccountName(body), handleToast);
          if (accountName && accountName.customer_name) {
            setBene(true);
            setDetails(accountName);
            setError("");
          } else {
            setBene(false);
            setDetails({});
            setError("Account name not found.");
          }
        } else if (country === "Nigeria" && selectedOption === "yellowCard") {
          try {
            const body = {
              accountNumber: accNum,
              networkId: networkID,
            };
            const accountName = await withApiErrorToast(GetAccountName(body), handleToast);
            if (accountName && accountName.accountName) {
              setBene(true);
              setDetails(accountName);
              setError("");
            } else {
              setBene(false);
              setDetails({});
              setError("Username not found.");
            }
          } catch (error) {
            setBene(false);
            setDetails({});
            setError("Error fetching account details.");
          }
        }
      } catch (error) {
        setBene(false);
        setDetails({});
        setError("Error fetching account details.");
      } finally {
        setLoading1(false);
      }
    }
  };
  useEffect(() => {
    getAccountName();
  }, [networkID, countryCode2, debouncedValue]);

  const getbanks = async () => {
    try {
      const res = await GetLinkBanks();
      if (res) {
        setServiceProvider(res[0].Banks);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (yellowCard === "NG" && selectedOption === "link") {
      getbanks();
      setIsManualMode(false);
    } else {
      const fetchData = async () => {
        if (selectedOption != "link" && yellowCard != "") {
          setLoading(true);
          const networks = await getNetwork(yellowCard);
          const channels = await getChannels(yellowCard);
          setIsManualMode(false);
          const ntw = networks
            .map((n: any) => ({
              ...n,
              matchingChannelCount: n.channelIds.filter((id: string) =>
                channels.some((channel) => channel.id === id)
              ).length,
            }))
            .filter((n: any) => n.matchingChannelCount > 0)
            .sort(
              (a: any, b: any) =>
                b.matchingChannelCount - a.matchingChannelCount
            );

          setServiceProvider(ntw);
          if (ntw.length === 1) {
            setCountryCode2(ntw[0].name);
            setNetworkID(ntw[0].id);
          } else if (ntw.length > 1) {
            setCountryCode2("");
            setNetworkID("");
          } else if (ntw.length === 0) {
            // Switch to manual mode
            setIsManualMode(true);
            setServiceProvider([]);
            const manualNetId = await getManualNetworkId(channels[0].id);
            if (manualNetId) {
              setManualNetworkId(manualNetId);
              setNetworkID(manualNetId);
            } else {
            }
          } else {
          }

          // // Check if networks are empty (manual mode required)
          // if (networks.length === 0) {

          // } else {
          //   // Normal mode with networks available
          // }
        }
      };
      fetchData();
    }
    setAccNum("");
    setCountryCode2("");
    setSelectedNote("");
    setBene(false);
    setIsProviderSelected(null);
  }, [yellowCard != "", selectedOption]);

  const validateForm = () => {
    let errorHandling = 1;
    const errors = {
      accErr: "",
      accNameErr: "",
      bankErr: "",
      noteError: "",
    };

    // Validate account number
    if (accNum.length === 0) {
      errors.accErr = "Account number is required";
      errorHandling = 0;
    } else if (accNum.length < 5) {
      errors.accErr = "Invalid account number";
      errorHandling = 0;
    }

    // Validate account name (for non-Nigeria or specific conditions)
    if (country !== "Nigeria" && accName.length === 0) {
      errors.accNameErr = "Account name is required";
    }

    // Validate bank
    if (isManualMode) {
      // In manual mode, TypedBank is required
      if (TypedBank.length === 0) {
        errors.bankErr = "Bank name is required";
        errorHandling = 0;
      }
    } else {
      // In normal mode, use existing validation
      if (serviceProvider.length === 0 && TypedBank.length === 0) {
        errors.bankErr = "Bank is required";
        errorHandling = 0;
      } else if (serviceProvider.length !== 0 && countryCode2.length === 0) {
        errors.bankErr = "Bank is required";
        errorHandling = 0;
      }
    }

    // Validate note
    if (selectedNote.length === 0) {
      errors.noteError = "Note is required";
      errorHandling = 0;
    }

    // Validate account name for Nigeria
    if (country === "Nigeria") {
      if (selectedOption === "link") {
        // Only validate if account number has been entered and bank has been selected
        if (
          accNum.length > 0 &&
          countryCode2.length > 0 &&
          (!details || !details.customer_name)
        ) {
          errors.accNameErr = "Account name is required";
          errorHandling = 0;
        }
      } else if (
        selectedOption === "yellowCard" &&
        accNum.length > 0 &&
        countryCode2.length > 0 &&
        (!details || !details.accountName)
      ) {
        errors.accNameErr = "Account name is required";
        errorHandling = 0;
      }
    }

    return { errorHandling, errors };
  };

  const valdateInput = () => {
    const { errorHandling, errors } = validateForm();

    // Set error messages
    setAccErr(errors.accErr);
    setAccNameErr(errors.accNameErr);
    setBankErr(errors.bankErr);
    setNoteError(errors.noteError);

    if (errorHandling === 1) {
      if (country === "Nigeria" && selectedOption === "link") {
        if (!details || !details.customer_name) {
          handleToast("Account does not exist", "error");
        } else {
          navigation.navigate("LinkSendAmount", {
            data: {
              bankName: countryCode2,
              accountName: details.customer_name,
              accountNumber: accNum,
              reason: selectedNote,
              bankImg: bankImg,
            },
          });
        }
      } else {
        const finalNetworkID = isManualMode ? manualNetworkId : networkID;
        const finalProvider = isManualMode
          ? TypedBank
          : serviceProvider.length === 0
          ? TypedBank
          : countryCode2;

          console.log('jjijiji',finalNetworkID);
          
        navigation.navigate("BankAmountScreen1", {
          country: country,
          channelID: channelID,
          currencyCode: currencyCode,
          symbol: symbol,
          networkID: finalNetworkID,
          provider: finalProvider,
          accNum: accNum.trim(),
          accName:
            selectedOption === "yellowCard" ? details.accountName : accName,
          type: "bank-account",
          note: selectedNote,
          aplhaCode2: aplhaCode2,
          isManualInput: isManualMode,
        });
      }
    } else {
    }
  };

  const getBeneficiaries = async () => {
    try {
      const response = await GetBeneficiaries(1, 10, "bank-account");    
      if (response.items) {
        setBeneficiaries(response?.items);
      } else {
        handleToast(response.message, "error");
      }
    } catch (error) {}
  };
  const AddBene = async () => {
    try {
      const body =
        country === "Nigeria" && selectedOption === "link"
          ? {
              account: accNum,
              providerName: countryCode2,
              name: details.customer_name,
              type: "bank-account",
              country: yellowCard,
            }
          : {
              account: accNum,
              providerName: isManualMode
                ? TypedBank
                : serviceProvider.length === 0
                ? TypedBank
                : countryCode2,
              name:
                selectedOption === "yellowCard" ? details.accountName : accName,
              type: "bank-account",
              country: yellowCard,
              yellowCardChannelId: channelID,
              yellowCardNetworkId: isManualMode ? manualNetworkId : networkID,
              isManualNetworkInput: isManualMode ? true : false,
            };
      const response = await AddBeneficiary(body);
      // console.log(body);
      if (response.account) {
        handleToast("Beneficiary saved", "success");
        getBeneficiaries();
      } else {
        handleToast(response.message, "error");
        setIsOn(false);
      }
    } catch (error) {}
  };

  const handleSwitch = () => {
    if (isOn) {
      setIsOn(false);
    } else {
      setIsOn(true);
      const { errorHandling, errors } = validateForm();

      // Set error messages
      setAccErr(errors.accErr);
      setAccNameErr(errors.accNameErr);
      setBankErr(errors.bankErr);
      setNoteError(errors.noteError);

      if (errorHandling === 1) {
        AddBene();
      } else {
        setIsOn(false);
      }
    }
  };
  const getCurrencyCode = (yc) => {
    if (yc === "TR" || yc === "TR") {
      return "TRY";
    }
    const country = countries.find((item) => item.YellowCardCode === yc);
    return country ? country.currencyCode : "Country not found";
  };
  const getSymbol = (currencyCode) => {
    if (currencyCode === "TR") {
      return "₺";
    }
    const curSymbol = countries.find(
      (item) => item.YellowCardCode === currencyCode
    );
    return curSymbol ? curSymbol.symbol : "Symbol not found";
  };

  const checkId = async () => {
    try {
      const res = await CheckId();
      if (res.bvn && res.nin) {
        setNextState(1);
      } else {
        setNextState(0);
      }
    } catch (error) {}
  };
  useFocusEffect(
    useCallback(() => {
      getBeneficiaries();
      checkId();
    }, [])
  );
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Bank account" navigation={navigation} />
        <ScrollView automaticallyAdjustKeyboardInsets={true}>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <TouchableOpacity
                onPress={() => {
                  setShowCountries(true);
                }}
              >
                <View>
                  <Input
                    value={country}
                    label="Country"
                    placeholder="Nigeria"
                    inputStyle={{ width: "65%", color: "#161817" }}
                    contStyle={{ marginBottom: 16 }}
                    editable={false}
                    leftIcon={
                      <Image
                        source={flag}
                        style={{
                          width: 24,
                          height: 24,
                          marginLeft: 14,
                          objectFit: "cover",
                          borderRadius: 100,
                          opacity: country === "" ? 0.5 : 1,
                        }}
                      />
                      //   <View>
                      //   </View>
                    }
                    rightIcon={
                      <View
                        style={{
                          //   backgroundColor: "red",
                          width: "15%",
                          height: "100%",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <SvgXml xml={svg.dropDown} />
                      </View>
                    }
                  />
                </View>
              </TouchableOpacity>

              <>
                {country === "Nigeria" && (
                  <>
                    {showMultiOption && (
                      <View style={styles.optionCont}>
                        <P
                          style={{
                            fontSize: 12,
                            fontFamily: fonts.poppinsRegular,
                            lineHeight: 18,
                            marginBottom: 6,
                          }}
                        >
                          Select preferred method
                        </P>

                        {options.map((item, index) => {
                          return (
                            <TouchableOpacity
                              key={index}
                              style={[
                                styles.option,
                                {
                                  borderColor:
                                    selectedOption === item.type
                                      ? colors.primary
                                      : "#E6E5E5",
                                },
                              ]}
                              onPress={() => {
                                setSelectedOption(item.type);
                                setServiceProvider([]);
                              }}
                            >
                              <View style={{ width: "80%", gap: 4 }}>
                                <View style={styles.opHolder}>
                                  <View
                                    style={{
                                      gap: 4,
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <SvgXml xml={svg.timer} />
                                    <P style={styles.opText}>Time</P>
                                  </View>
                                  <View style={{ width: 100 }}>
                                    <P style={styles.vText}>{item.time}</P>
                                  </View>
                                </View>
                                <View style={styles.opHolder}>
                                  <View
                                    style={{
                                      gap: 4,
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <SvgXml xml={svg.rate} />
                                    <P style={styles.opText}>Rate</P>
                                  </View>
                                  <View style={{ width: 100 }}>
                                    <P style={styles.vText}>{item.rate}</P>
                                  </View>
                                </View>
                                <View style={styles.opHolder}>
                                  <View
                                    style={{
                                      gap: 4,
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <SvgXml xml={svg.dolls} />
                                    <P style={styles.opText}>Min - max</P>
                                  </View>
                                  <View style={{ width: 100 }}>
                                    <P style={styles.vText}>{item.minMax}</P>
                                  </View>
                                </View>
                                <View style={styles.opHolder}>
                                  <View
                                    style={{
                                      gap: 4,
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <SvgXml xml={svg.paper} />
                                    <P style={styles.opText}>Requirement</P>
                                  </View>
                                  <View style={{ width: 100 }}>
                                    <P style={styles.vText}>
                                      {item.requirement}
                                    </P>
                                  </View>
                                </View>
                              </View>
                              <SvgXml
                                width={16}
                                height={16}
                                xml={
                                  selectedOption === item.type
                                    ? svg.checked
                                    : svg.check
                                }
                              />
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                    )}
                  </>
                )}
                <>
                  {showD && (
                    <>
                      <Input
                        value={accNum}
                        onChangeText={(e) => {
                          setAccNum(e);
                          setAccErr("");
                        }}
                        label={"Account number"}
                        placeholder={"**********"}
                        keyboardType="numeric"
                        error={accErr != "" ? true : false}
                      />
                      {accErr && <P style={styles.errorText}>{accErr}</P>}
                      <TouchableOpacity
                        onPress={() => {
                          setShow2(true);
                        }}
                      >
                        <Input
                          value={countryCode2}
                          label="Select bank"
                          placeholder={
                            country === "Nigeria" && selectedOption === "link"
                              ? "Palmpay"
                              : "First Bank"
                          }
                          inputStyle={{ width: "65%", color: "#161817" }}
                          contStyle={{ marginTop: 16 }}
                          editable={false}
                          error={bankErr != "" ? true : false}
                          leftIcon={
                            <View>
                              {country === "Nigeria" &&
                              selectedOption == "link" ? (
                                <Image
                                  source={{ uri: bankImg }}
                                  style={{
                                    width: 28,
                                    height: 28,
                                    marginLeft: 14,
                                    objectFit: "contain",
                                  }}
                                />
                              ) : (
                                <SvgXml
                                  xml={svg.bank2}
                                  style={{ marginLeft: 14 }}
                                />
                              )}
                            </View>
                          }
                          rightIcon={
                            <View
                              style={{
                                width: "15%",
                                height: "100%",
                                justifyContent: "center",
                                alignItems: "center",
                              }}
                            >
                              <SvgXml xml={svg.dropDown} />
                            </View>
                          }
                        />
                      </TouchableOpacity>
                      {bankErr && <P style={styles.errorText}>{bankErr}</P>}
                      {error ? (
                        <View style={{ width: "100%" }}>
                          <P
                            style={{
                              color: colors.red,
                              marginTop: 8,
                              textAlign: "left",
                              fontSize: 12,
                            }}
                          >
                            {error}
                          </P>
                        </View>
                      ) : null}
                      {loading1 ? (
                        <ContentLoader
                          style={{ marginBottom: 16, marginTop: 16 }}
                          width={"100%"}
                          height={44}
                          speed={2}
                          backgroundColor="#F7F4FF"
                          foregroundColor="#ecebeb"
                        >
                          <Rect
                            x="0"
                            y="0"
                            rx="4"
                            ry="4"
                            width="100%"
                            height="44"
                          />
                        </ContentLoader>
                      ) : (
                        bene && (
                          <View style={styles.benefeciary}>
                            {/* @ts-ignore */}
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            >
                              {/* @ts-ignore */}
                              <P>{`${
                                details?.accountName || details?.customer_name
                              }`}</P>
                            </View>
                            <SvgXml xml={svg.green_check} />
                          </View>
                        )
                      )}
                      <TouchableOpacity
                        onPress={() => {
                          setShowNotes(true);
                        }}
                      >
                        <Input
                          label={<P style={styles.label}>Note</P>}
                          contStyle={{ marginTop: 16 }}
                          placeholder="Enter note here..."
                          editable={false}
                          value={selectedNote}
                          error={noteError != "" ? true : false}
                          rightIcon={
                            <View
                              style={{
                                width: "15%",
                                height: "100%",
                                justifyContent: "center",
                                alignItems: "center",
                              }}
                            >
                              <SvgXml xml={svg.dropDown} />
                            </View>
                          }
                        />
                        {noteError && (
                          <P style={styles.errorText}>{noteError}</P>
                        )}
                      </TouchableOpacity>
                      <View style={styles.switchCase}>
                        <CustomSwitch1 isOn={isOn} onToggle={handleSwitch} />
                        <P
                          style={{
                            marginLeft: 4,
                            fontSize: 12,
                            color: colors.gray,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          Save Baneficiary
                        </P>
                      </View>
                    </>
                  )}
                </>
              </>
              {country !== "Nigeria" && (
                <>
                  {country != "" && country != undefined && country != null && (
                    <>
                      <Input
                        value={accNum}
                        onChangeText={(e) => {
                          setAccNum(e);
                          setAccErr("");
                        }}
                        label={country === "Turkey" ? "IBAN" : "Account number"}
                        placeholder={
                          country === "Turkey"
                            ? "TR96 0001 1231 1046"
                            : "**********"
                        }
                        keyboardType="numeric"
                        error={accErr != "" ? true : false}
                        rightIcon={
                          country === "Turkey" ? (
                            <TouchableOpacity
                              onPress={() => {
                                setShowQrCode(true);
                              }}
                            >
                              <SvgXml
                                xml={svg.qrcode}
                                style={{ marginLeft: 10 }}
                              />
                            </TouchableOpacity>
                          ) : (
                            <></>
                          )
                        }
                      />
                      {accErr && <P style={styles.errorText}>{accErr}</P>}
                      {serviceProvider.length === 0 || isManualMode ? (
                        <>
                          <Input
                            value={TypedBank}
                            label="Enter bank name"
                            placeholder={"First Bank of Nigeria"}
                            inputStyle={{ width: "65%", color: "#161817" }}
                            contStyle={{ marginTop: 16 }}
                            error={bankErr != "" ? true : false}
                            onChangeText={(e) => {
                              setTypedBank(e);
                              setBankErr("");
                            }}
                            leftIcon={
                              <View>
                                <SvgXml
                                  xml={svg.bank2}
                                  style={{ marginLeft: 14 }}
                                />
                              </View>
                            }
                          />
                          {isManualMode && (
                            <View style={{ marginTop: 8 }}>
                              <NoteComponent2 text="ℹ️ This country requires manual bank entry. Please type the bank name exactly as it appears." />
                            </View>
                          )}
                        </>
                      ) : (
                        <TouchableOpacity
                          onPress={() => {
                            setShow2(true);
                          }}
                        >
                          <Input
                            value={countryCode2}
                            label="Select bank"
                            placeholder={
                              country === "Nigeria"
                                ? "Palmpay"
                                : "SFx money app"
                            }
                            inputStyle={{ width: "65%", color: "#161817" }}
                            contStyle={{ marginTop: 16 }}
                            editable={false}
                            error={bankErr != "" ? true : false}
                            leftIcon={
                              <View>
                                <SvgXml
                                  xml={svg.bank2}
                                  style={{ marginLeft: 14 }}
                                />
                              </View>
                            }
                            rightIcon={
                              <View
                                style={{
                                  width: "15%",
                                  height: "100%",
                                  justifyContent: "center",
                                  alignItems: "center",
                                }}
                              >
                                <SvgXml xml={svg.dropDown} />
                              </View>
                            }
                          />
                        </TouchableOpacity>
                      )}

                      {bankErr && <P style={styles.errorText}>{bankErr}</P>}
                      {error ? (
                        <View style={{ width: "100%" }}>
                          <P
                            style={{
                              color: colors.red,
                              marginTop: 8,
                              textAlign: "left",
                              fontSize: 12,
                            }}
                          >
                            {error}
                          </P>
                        </View>
                      ) : null}
                      <>
                        <Input
                          value={accName}
                          onChangeText={(e) => {
                            setAccName(e);
                            setAccNameErr("");
                          }}
                          label={"Account name"}
                          placeholder={"John doe"}
                          contStyle={{ marginTop: 16 }}
                          error={accNameErr != "" ? true : false}
                        />
                        {accNameErr && (
                          <P style={styles.errorText}>{accNameErr}</P>
                        )}
                        <View style={{ marginTop: 8 }}>
                          <NoteComponent2 text="⚠️ Type the account name exactly as it appears on the account. Any mistake might result in loss of funds." />
                        </View>
                      </>
                      <TouchableOpacity
                        onPress={() => {
                          setShowNotes(true);
                        }}
                      >
                        <Input
                          label={<P style={styles.label}>Note</P>}
                          contStyle={{ marginTop: 16 }}
                          placeholder="Enter note here..."
                          editable={false}
                          value={selectedNote}
                          error={noteError != "" ? true : false}
                          rightIcon={
                            <View
                              style={{
                                width: "15%",
                                height: "100%",
                                justifyContent: "center",
                                alignItems: "center",
                              }}
                            >
                              <SvgXml xml={svg.dropDown} />
                            </View>
                          }
                        />
                        {noteError && (
                          <P style={styles.errorText}>{noteError}</P>
                        )}
                      </TouchableOpacity>
                      <View style={styles.switchCase}>
                        <CustomSwitch1 isOn={isOn} onToggle={handleSwitch} />
                        <P
                          style={{
                            marginLeft: 4,
                            fontSize: 12,
                            color: colors.gray,
                            fontFamily: fonts.poppinsRegular,
                            marginBottom: country === "Turkey" ? 16 : 0,
                          }}
                        >
                          Save Baneficiary
                        </P>
                      </View>
                    </>
                  )}
                </>
              )}
              {country === "Turkey" && (
                <NoteComponent2 text="Only send money to an account matching your SFx account name" />
              )}
            </View>
            <View style={{ width: "90%", alignSelf: "center", marginTop: 32 }}>
              <Button
                btnText="Next"
                disabled={
                  country === "" ||
                  yellowCard === "" ||
                  (yellowCard === "NG" && selectedOption === null)
                }
                onPress={() => {
                  if (selectedOption !== "link" && gateWayError) {
                    setShowChannelError(true);
                  } else {
                    if (showMultiOption) {
                      if (NextState === 0 && selectedOption === "yellowCard") {
                        setShowReq(true);
                        setShowMultiOption(false);
                      } else {
                        setShowD(true);
                        setShowMultiOption(false);
                      }
                    } else {
                      valdateInput();
                    }
                  }
                }}
              />
            </View>

            <View style={[styles.beneficiaryCont, { minHeight: 246 }]}>
              <View style={styles.beneNav}>
                <P style={{ color: colors.gray, fontSize: 12, lineHeight: 18 }}>
                  Beneficiaries
                </P>
                {country != "Turkey" && beneficiaries.length > 0 && (
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    <TouchableOpacity
                      onPress={() => navigation.navigate("Beneficiaries", {
                        actT: "bank-account",
                      })}
                    >
                      <P
                        style={{
                          color: "#8B52FF",
                          textDecorationLine: "underline",
                          alignItems: "center",
                          fontSize: 12,
                        }}
                      >
                        View all
                      </P>
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {beneficiaries.length === 0 ? (
                <View style={styles.emptyCont}>
                  <SvgXml xml={svg.userGroup} />
                  <P
                    style={{
                      fontFamily: fonts.poppinsMedium,
                      lineHeight: 21,
                      marginTop: 16,
                      fontSize: 12,
                    }}
                  >
                    No beneficiary!
                  </P>
                  <P
                    style={{
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                      color: colors.gray2,
                    }}
                  >
                    You have no beneficiary yet
                  </P>
                </View>
              ) : (
                <>
                  {beneficiaries.slice(0, 4).map((item, index) => {
                    const provider = TestBanks.BankList[0].banks.find(
                      (it) => it.name === item.providerName
                    );
                    const bImg = provider ? provider.image : null;
                    return (
                      <TouchableOpacity
                        key={index}
                        onPress={() => {
                          if (
                            item.country === "NG" &&
                            !item.yellowCardChannelId
                          ) {
                            navigation.navigate("LinkSendAmount", {
                              data: {
                                bankName: item?.providerName,
                                accountName: item?.name,
                                accountNumber: item?.account?.trim(),
                                reason: "Other",
                                bankImg: bImg,
                              },
                            });
                          } else {
                            navigation.navigate("BankAmountScreen1", {
                              country: item?.country,
                              channelID: item?.yellowCardChannelId,
                              currencyCode: getCurrencyCode(item?.country),
                              symbol: getSymbol(item?.country),
                              networkID: item?.yellowCardNetworkId,
                              provider: item?.providerName,
                              accNum: item?.account?.trim(),
                              accName: item?.name,
                              note: "Other",
                              aplhaCode2: item?.country,
                              isManualInput:
                                item?.isManualNetworkInput || false,
                            });
                          }
                        }}
                      >
                        {bImg === null ? (
                          <BenediciaryComponent
                            svg={svg.bankGreen}
                            // img={{ uri: bImg }}
                            text1={item.name}
                            text2={`${item.account} | ${item.providerName}`}
                          />
                        ) : (
                          <BenediciaryComponent
                            // svg={svg.bankGreen}
                            img={{ uri: bImg }}
                            text1={item.name}
                            text2={`${item.account} | ${item.providerName}`}
                          />
                        )}
                      </TouchableOpacity>
                    );
                  })}
                </>
              )}
            </View>
          </View>
        </ScrollView>
      </Div>
      {showReq && (
        <ExtraId
          extraFunction={() => {
            setShowReq(false);
            setShowMultiOption(false);
            setShowD(true);
          }}
          close={() => {
            setShowReq(false);
            setShowMultiOption(true);
          }}
        />
      )}
      {loader && <Loader />}
      <BottomSheet
        isVisible={showCountries}
        showBackArrow={false}
        backspaceText="Select country"
        onClose={() => setShowCountries(false)}
        modalContentStyle={{ height: "75%" }}
        extraModalStyle={{ height: "73%" }}
        componentHolderStyle={{ flex: 1 }}
        components={
          <CountrySelect
            excludedCountries={NotBaOUTCountires}
            onActiveCountryChange={handleActiveCountry}
            onActiveFlag={handleActiveFlag}
            onActiveYellowCard={handleActiveYellowCard}
            onActiveCurrencyCode={handleActiveCurrencyCode}
            onSymbolChange={handleActiveSymbol}
            onActiveAlphaCode2Change={handleActiveAplhaCode2}
            onPress={(index: number) => {
              // Important: Close the modal AFTER all the callbacks have been processed
              setTimeout(() => {
                setShowCountries(false);
              }, 100);
            }}
          />
        }
      />
      <Loader loading={true} visible={loading} />
      <BottomSheet
        isVisible={show2}
        onClose={() => setShow2(false)}
        backspaceText="Provider"
        showBackArrow={false}
        modalContentStyle={{ height: "75%" }}
        extraModalStyle={{ height: "73%" }}
        components={
          <View>
            <View style={styles.search}>
              <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search bank"
                cursorColor={colors.black}
                value={searchQuery}
                onChangeText={(text) => setSearchQuery(text)}
              />
            </View>
            <P
              style={{
                marginTop: 16,
                fontSize: 12,
                lineHeight: 19.2,
                color: colors.gray,
              }}
            >
              Select your bank
            </P>
            <ScrollView
              contentContainerStyle={{ paddingBottom: 500 }}
              showsVerticalScrollIndicator={false}
              automaticallyAdjustContentInsets={true}
            >
              {filteredBank.map((item, index) => {
                return country === "Nigeria" && selectedOption === "link" ? (
                  <ListItemSelect
                    key={index}
                    text1={item?.name}
                    imgStyle={{ width: 30, height: 30 }}
                    image={{ uri: item.image }}
                    onPress={() => {
                      setIsProviderSelected(index);
                      setBankImg(item.image);
                      setCountryCode2(item.name);
                      setBankCode(item.value);
                      setShow2(false);
                      setBankErr("");
                    }}
                    containerStyle={{
                      marginBottom: 16,
                      marginTop: index == 0 ? 6 : 0,
                    }}
                    isActive={isproviderSelected == index}
                  />
                ) : (
                  <ListItemSelect
                    key={index}
                    text1={item.name}
                    icon={svg.bank2}
                    onPress={() => {
                      setIsProviderSelected(index);
                      setCountryCode2(item.name);
                      setNetworkID(item.id);
                      setShow2(false);
                      setBankErr("");
                    }}
                    containerStyle={{
                      marginBottom: 16,
                      marginTop: index == 0 ? 6 : 0,
                    }}
                    isActive={isproviderSelected == index}
                  />
                );
              })}
            </ScrollView>
          </View>
        }
      />
      <BottomSheet
        isVisible={showNotes}
        showBackArrow={false}
        onClose={() => setShowNotes(false)}
        modalContentStyle={{ height: "65%" }}
        extraModalStyle={{ height: "63%" }}
        components={
          <ScrollView
            contentContainerStyle={{ paddingBottom: 200 }}
            showsVerticalScrollIndicator={false}
          >
            <View style={{ paddingTop: 16 }}>
              {notes.length === 0 ? (
                <>
                  <P>No available note</P>
                </>
              ) : (
                <>
                  {notes.map((item, index) => {
                    return (
                      <ListItemSelect
                        key={index}
                        text1={item}
                        onPress={() => {
                          setSelectedNote(item);
                          setShowNotes(false);
                          setNoteError("");
                        }}
                        containerStyle={{
                          marginBottom: 16,
                          marginTop: index == 0 ? 6 : 0,
                        }}
                        isActive={selectedNote == item}
                      />
                    );
                  })}
                </>
              )}
            </View>
          </ScrollView>
        }
      />
      <BottomSheet
        isVisible={showChannelError}
        showBackArrow={false}
        backspaceText=""
        onClose={() => setShowChannelError(false)}
        modalContentStyle={{ height: "55%" }}
        extraModalStyle={{ height: "53%" }}
        components={
          <View
            style={{
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
              paddingTop: 34,
            }}
          >
            <SvgXml xml={svg.noCloud} width={44} height={44} />
            <P style={{ marginTop: 16 }}>Withdrawal option unavailable!</P>
            <P
              style={{
                textAlign: "center",
                fontSize: 12,
                color: colors.gray,
                marginTop: 4,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              We apologize for any inconvenience this may cause and appreciate
              your patience while we work to enhance our payment options.
            </P>

            <View style={{ marginTop: 32 }}>
              <Button
                btnText="Explore other payment options"
                onPress={() => {
                  setShowChannelError(false);
                  navigation.pop();
                }}
              />
            </View>
          </View>
        }
      />
      {showQrCode && (
        <BarCodeScanner
          visible={showQrCode}
          onClose={() => {
            setShowQrCode(false);
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: "100%",
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  pyItem: {
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 16,
    alignItems: "center",
    flexDirection: "row",
    padding: 16,
  },
  pyName: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  rate: {
    lineHeight: 18,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  noteCont: {
    width: "100%",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 8,
  },
  label: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
  switchCase: {
    width: "100%",
    // backgroundColor: "red",
    alignItems: "flex-start",
    flexDirection: "row",
    marginTop: 16,
  },
  beneficiaryCont: {
    width: "90%",
    backgroundColor: colors.white,
    borderRadius: 12,
    alignSelf: "center",
    marginTop: 60,
    minHeight: 246,
    paddingBottom: 20,
  },
  beneNav: {
    width: "100%",
    paddingTop: 11,
    paddingLeft: 24,
    paddingRight: 16,
    paddingBottom: 13,
    flexDirection: "row",
    justifyContent: "space-between",
    borderBottomWidth: 1,
    borderColor: colors.stroke,
  },
  emptyCont: {
    width: "100%",
    height: 204,
    alignItems: "center",
    justifyContent: "center",
  },
  benefeciary: {
    paddingTop: 4,
    paddingRight: 24,
    paddingBottom: 4,
    paddingLeft: 24,
    backgroundColor: "#F7F4FF",
    borderRadius: 8,
    flexDirection: "row",
    width: "100%",
    height: 44,
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 16,
  },
  search: {
    width: "100%",
    height: (5.5 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    marginTop: 24,
    borderWidth: 1,
    borderRadius: 99,
    borderColor: colors.stroke,
    padding: 12,
    paddingLeft: 14,
    paddingRight: 14,
    flexDirection: "row",
  },
  searchInput: {
    width: "90%",
    height: 24,
    fontFamily: fonts.poppinsRegular,
    lineHeight: 24,
    fontSize: 14,
  },
  image: {
    width: 32,
    height: 32,
    borderRadius: 100,
    marginRight: 16,
    objectFit: "cover",
  },
  errorText: {
    color: colors.red,
    fontSize: 10,
    marginTop: 5,
    fontFamily: fonts.poppinsRegular,
    // marginTop: -30,
  },
  optionCont: {
    width: "100%",
    marginTop: 16,
  },
  option: {
    width: "100%",
    paddingLeft: 12,
    paddingRight: 12,
    paddingTop: 8,
    paddingBottom: 8,
    borderWidth: 1,
    borderColor: "#E6E5E5",
    borderRadius: 8,
    marginBottom: 16,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  opText: {
    fontSize: fontScale == 1 ? 11 : fontScale > 1 ? 10 : 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  opHolder: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  vText: {
    textAlign: "left",
    fontSize: fontScale == 1 ? 11 : fontScale > 1 ? 10 : 12,
  },
});
