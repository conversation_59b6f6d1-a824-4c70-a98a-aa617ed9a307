import React, { useState, useEffect, useContext, useCallback } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  BackHandler,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import Link from "../../components/Link";
import BottomComponent from "../../components/BottomComponent";
import BottomSheet from "../../components/BottomSheet";
import BottomSheetComponent from "../../components/BottomSheetComponent";
import ShareOption from "../../components/ShareOption";
import LongSheet from "../../components/LongSheet";
import H4 from "../../components/H4";
import * as Clipboard from "expo-clipboard";
import NoteComponent from "../../components/NoteComponent";
import NoteComponent2 from "../../components/NoteComponent2";
import { DepositContext } from "../../context/DepositeContext";
import { useFocusEffect } from "@react-navigation/native";
import {
  formatNumberWithCommas,
  formatToTwoDecimals,
} from "../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");

export default function BankTransferScreen({ navigation, route }) {
  const { country } = route.params;
  const { bankInfo } = route?.params || {};
  const { ngnRate } = route?.params || "";
  const { currencyCode } = route?.params || "";
  const { symbol } = route?.params || "";
  const { inputValue } = route?.params || "";
  const { transaction } = route?.params || {};
  const { fee } = route?.params || {};
  const [showBtmSheet, setShowBtmSheet] = useState(false);
  const [moneyAdded, setMoneyAdded] = useState(false);
  const [isRefCopied, setIsRefCopied] = useState(false);
  const [isAccCopied, setIsAccCopied] = useState(false);
  const [isBankCopied, setIsBankCopied] = useState(false);
  const [isAccountNameCopied, setIsAccountNameCopied] = useState(false);
  const [isAmountCopied, setIsAmountCopied] = useState(false);
  const [refNUm, setRefNum] = useState("SF**********");
  const [accNum, setAccNum] = useState("**********");
  const { deposit } = useContext(DepositContext);
  const formatNumber = (value) => {
    value = value?.toString();
    return (
      value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0
    );
  };
  const copyAccNum = async (text, type) => {
    const copiedText = await Clipboard.setStringAsync(text);
    if (copiedText === true) {
      if (type === "bank") {
        setIsBankCopied(true);
      } else if (type === "accountName") {
        setIsAccountNameCopied(true);
      } else {
        setIsAccCopied(true);
      }
      setTimeout(() => {
        setIsAccCopied(false);
        setIsBankCopied(false);
        setIsAccountNameCopied(false);
      }, 4000);
    }
  };
  // @ts-ignore
  const copyRefNum = async () => {
    const copiedText = await Clipboard.setStringAsync(transaction.ref);

    if (copiedText === true) {
      setIsRefCopied(true);
      setTimeout(() => {
        setIsRefCopied(false);
      }, 4000);
    }
  };

  // Function to copy the converted amount (only the number, no currency symbol or code)
  const copyConvertedAmount = async () => {
    // Format the amount but don't include the currency symbol or code
    const amountToCopy = `${
      formatNumberWithCommas(Number(transaction?.localAmount)) || 0
    }`;
    const copiedText = await Clipboard.setStringAsync(amountToCopy);

    if (copiedText === true) {
      setIsAmountCopied(true);
      setTimeout(() => {
        setIsAmountCopied(false);
      }, 4000);
    }
  };
  // const [timeLeft, setTimeLeft] = useState(0);
  const [timeLeft1, setTimeLeft1] = useState(25 * 60); // 25 minutes in seconds

  // useEffect(() => {
  //   const intervalId = setInterval(() => {
  //     setTimeLeft((prevTimeLeft) => {
  //       if (prevTimeLeft <= 1) {
  //         clearInterval(intervalId);
  //         return 0;
  //       }
  //       return prevTimeLeft - 1;
  //     });
  //   }, 1000);

  //   return () => clearInterval(intervalId);
  // }, []);
  // useEffect(() => {
  //   const intervalId = setInterval(() => {
  //     setTimeLeft1((prevTimeLeft) => {
  //       if (prevTimeLeft <= 1) {
  //         clearInterval(intervalId);
  //         return 0;
  //       }
  //       return prevTimeLeft - 1;
  //     });
  //   }, 1000);

  //   return () => clearInterval(intervalId);
  // }, []);
  const [timeLeft, setTimeLeft] = useState(24 * 60 * 60); // 24 hours in seconds

  useEffect(() => {
    if (transaction.payment?.source) {
      setAccNum(transaction.payment.source.accountNumber);
    }

    // Start the countdown
    const intervalId = setInterval(() => {
      setTimeLeft((prevTimeLeft) => {
        if (prevTimeLeft <= 1) {
          clearInterval(intervalId);
          return 0;
        }
        return prevTimeLeft - 1;
      });
    }, 1000);

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [transaction?.payment?.source]);

  const formatTime = (time: number): string => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = time % 60;
    return `${hours}:${minutes < 10 ? "0" : ""}${minutes}:${
      seconds < 10 ? "0" : ""
    }${seconds}`;
  };
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        // Reset navigation stack to avoid loading issues
        // @ts-ignore
        navigation.reset({
          index: 0,
          routes: [{ name: "BottomTabNavigator" }],
        });
        return true;
      };
      // Disable iOS swipe back gesture
      navigation.setOptions({
        gestureEnabled: false,
      });
      // Handle Android back button
      BackHandler.addEventListener("hardwareBackPress", onBackPress);

      return () => {
        BackHandler.removeEventListener("hardwareBackPress", onBackPress);
      };
    }, [navigation])
  );
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Bank transfer"
          navigation={navigation}
          goHome={true}
        />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Send money to SFx money app"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      ${formatToTwoDecimals(Number(inputValue)) || 0}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <P
                        style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}
                      >
                        {symbol}
                        {formatNumberWithCommas(
                          Number(transaction?.localAmount)
                        ) || 0}
                      </P>
                      <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                        {currencyCode}
                      </P>
                    </View>

                    <TouchableOpacity
                      onPress={copyConvertedAmount}
                      style={[
                        styles.copyBtn,
                        { marginLeft: 8, position: "relative" },
                      ]}
                    >
                      <View style={styles.copyBtn}>
                        <SvgXml
                          xml={isAmountCopied ? svg.circleSuccess : svg.copy}
                          style={{ width: 14, height: 14 }}
                        />
                      </View>
                    </TouchableOpacity>
                  </View>
                }
                timer={
                  <View
                    style={[
                      styles.timer,
                      {
                        backgroundColor: colors.secBackground,
                      },
                    ]}
                  >
                    <P style={styles.statusText}>
                      Expires in{" "}
                      <P
                        // @ts-ignore
                        style={[
                          styles.statusText,
                          {
                            color: colors.primary,
                            fontFamily: fonts.poppinsMedium,
                          },
                        ]}
                      >
                        {formatTime(timeLeft)}
                      </P>
                    </P>
                  </View>
                }
                bottomComponent={
                  <>
                    <NoteComponent2 text="Send payment to account below. Include reference number." />
                    <View style={styles.desCont}>
                      <View style={styles.items}>
                        <SvgXml
                          xml={svg.userSquare}
                          style={{ marginRight: 8 }}
                        />
                        <View>
                          <P style={styles.holder}>Account name</P>
                          <P style={styles.value}>
                            {country === "Turkey" ? "" : bankInfo.accountName}
                          </P>
                        </View>
                        <TouchableOpacity
                          onPress={() => {
                            copyAccNum(bankInfo.accountName, "accountName");
                          }}
                          style={styles.copyBtn}
                        >
                          <View style={styles.copyBtn}>
                            <P style={styles.copyText}>
                              {isAccountNameCopied ? "Copied" : "Copy"}
                            </P>

                            <SvgXml
                              xml={
                                isAccountNameCopied
                                  ? svg.circleSuccess
                                  : svg.copy
                              }
                              style={{ width: 14, height: 14 }}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View style={styles.items}>
                        <SvgXml xml={svg.bank} style={{ marginRight: 8 }} />
                        <View>
                          <P style={styles.holder}>Bank</P>
                          <P style={styles.value}>
                            {country === "Turkey" ? "" : bankInfo.name}
                          </P>
                        </View>
                      </View>
                      <View style={[styles.items, { marginBottom: 0 }]}>
                        <SvgXml
                          xml={svg.userBrown}
                          style={{ marginRight: 8 }}
                        />
                        <View>
                          <P style={styles.holder}>
                            {country === "Turkey" ? "IBAN" : "Account number"}
                          </P>
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { width: (45 * width) / 100 },
                            ]}
                          >
                            {bankInfo.accountNumber}
                          </P>
                        </View>
                        <TouchableOpacity
                          onPress={() => {
                            copyAccNum(bankInfo.accountNumber, "accc");
                          }}
                          style={styles.copyBtn}
                        >
                          <View style={styles.copyBtn}>
                            <P style={styles.copyText}>
                              {isAccCopied ? "Copied" : "Copy"}
                            </P>

                            <SvgXml
                              xml={isAccCopied ? svg.circleSuccess : svg.copy}
                              style={{ width: 14, height: 14 }}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          width: "100%",
                          height: 1,
                          borderBottomWidth: 1,
                          borderColor: colors.stroke,
                          borderStyle: "dashed",
                          marginTop: 24,
                          marginBottom: 24,
                        }}
                      ></View>
                      <View style={[styles.items, { marginBottom: 0 }]}>
                        <View>
                          <P style={styles.holder}>Reference number</P>
                          <P
                            // @ts-ignore
                            style={[
                              styles.value,
                              { width: (45 * width) / 100 },
                            ]}
                          >
                            {transaction.ref}
                          </P>
                        </View>
                        <TouchableOpacity
                          onPress={copyRefNum}
                          style={styles.copyBtn}
                        >
                          <View style={styles.copyBtn}>
                            <P style={styles.copyText}>
                              {isRefCopied ? "Copied" : "Copy"}
                            </P>

                            <SvgXml
                              xml={isRefCopied ? svg.circleSuccess : svg.copy}
                              style={{ width: 14, height: 14 }}
                            />
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </>
                }
              />
              {/* <P
                style={{
                  textAlign: "center",
                  marginTop: 16,
                  fontSize: 12,
                  lineHeight: 18,
                  color: colors.gray,
                }}
              >
                Don’t disclosed until payment is made
              </P> */}
              <View style={styles.buttonWrap}>
                <Button
                  btnText="I’ve sent the money"
                  onPress={() =>
                    navigation.navigate("MoneySentScreen1", {
                      transaction: transaction,
                      ngnRate: ngnRate,
                      currencyCode: currencyCode,
                      symbol: symbol,
                      inputValue: inputValue,
                    })
                  }
                />
              </View>

              {/* <Link
                style={{
                  textAlign: "center",
                  marginTop: (3.5 * height) / 100,
                  fontSize: 12,
                }}
                onPress={() =>
                  navigation.navigate("BankTransactionDetails2", {})
                }
              >
                View details
              </Link> */}
            </View>
          </View>
        </ScrollView>
      </Div>

      <BottomSheetComponent
        isVisible={showBtmSheet}
        onClose={() => setShowBtmSheet(false)}
        backspaceText="Share to"
        components={<ShareOption />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.secBackground,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
    marginTop: 16,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    padding: 8,
    alignItems: "center",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
    color: colors.primary,
  },
  contentCard: {
    width: "90%",
    alignSelf: "center",
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 12,
    // marginTop: 24,
    marginTop: (2.7 * height) / 100,
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 16,
    paddingRight: 16,
  },
  section1Wrap: {
    alignItems: "center",
    justifyContent: "center",
  },
  addMoney: {
    fontSize: 12,
    lineHeight: 19.2,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amt: {
    fontSize: (4 * height) / 100,
    // fontSize: 32,
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  amtCur: {
    lineHeight: 48,
    fontFamily: fonts.poppinsMedium,
  },
  timer: {
    paddingTop: 4,
    paddingBottom: 4,
    paddingLeft: 16,
    paddingRight: 16,
    borderRadius: 99,
    marginTop: 16,
  },
  statusText: {
    fontSize: 10,
    lineHeight: 16,
    fontFamily: fonts.poppinsRegular,
  },
  section2Wrap: {
    width: "100%",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    marginTop: (2.7 * height) / 100,
    paddingBottom: (2.7 * height) / 100,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
  },
  section3Wrap: {
    width: "100%",
    marginTop: (2.7 * height) / 100,
    borderColor: colors.stroke,
    paddingLeft: 16,
  },
  barCont: {
    // backgroundColor: "red",
    alignItems: "center",
  },
  bar1: {
    height: 46,
    width: 2,
    borderRadius: 2,
    backgroundColor: colors.green,
    marginTop: 4,
    marginBottom: 4,
  },
  bar2: {
    height: 28,
    width: 2,
    borderRadius: 2,
    marginTop: 4,
    marginBottom: 4,
  },
  progressDesCont: {
    flexDirection: "row",
  },
  progTextHead: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  progTextBody: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  btnCont: {
    width: "80%",
    alignSelf: "center",
    marginTop: (5 * height) / 100,
    // marginTop: 42,
    // alignItems: 'center',
    // justifyContent: 'center',
  },
});
