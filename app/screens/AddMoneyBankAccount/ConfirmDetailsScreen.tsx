import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import { DepositBank } from "../../RequestHandlers/Wallet";
import { GetYellowCardEstimatedFee } from "../../RequestHandlers/Wallet";
import { useToast } from "../../context/ToastContext";
import { useGlobalModal } from "../../context/GlobalModalContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { formatNumberWithCommas, formatToTwoDecimals } from "../../Utils/numberFormat";

const { width, height } = Dimensions.get("window");

export default function ConfirmDetailScreen({ navigation, route }) {
  const {
    country,
    inputValue,
    currencyCode,
    ngnRate,
    networkID,
    symbol,
    aplhaCode2,
    isUsdInput,
  } = route.params;
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const [fee, setFee] = useState<any>([]);
  const { showAddMoneyFailedModal } = useGlobalModal();
  const formatNumber = (value) => {
    value = value?.toString() || "0";
    return (
      value?.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0
    );
  };
  function capitalizeFirstLetter(sentence) {
    if (!sentence) return sentence;
    return sentence.charAt(0).toUpperCase() + sentence.slice(1) || "....";
  }
  const getFee = async () => {
    try {
      const res = await withApiErrorToast(GetYellowCardEstimatedFee(
        aplhaCode2,
        inputValue,
        "bank",
        "DEPOSIT",
        isUsdInput ? "USD" : currencyCode
      ), handleToast);
      if (res.fee) {
        setFee(res);
      }
      if (res.error) {
        handleToast(res.message, "error");
      }
    } catch (error) {}
  };
  const deposit = async () => {
    setLoading(true);
    try {
      const body = {
        channelId: networkID,
        amount: Number(inputValue),
        currency: currencyCode,
        paymentGateway: "bank",
        country: aplhaCode2,
        amountCurrency: isUsdInput ? "USD" : currencyCode
      };
      const deposite = await withApiErrorToast(DepositBank(body), handleToast);
      if (deposite.payment) {
        setLoading(false);
        navigation.navigate("BankTransferScreen", {
          country: country,
          bankInfo: deposite.payment.bankInfo,
          inputValue: inputValue,
          currencyCode: currencyCode,
          ngnRate: ngnRate,
          symbol: symbol,
          transaction: deposite.transaction,
          fee: fee,
          amountCurrency: isUsdInput ? "USD" : currencyCode
        });
      } else {
        setLoading(false);
        const errorMessage = deposite.message;
        // Handle case where message might be an array
        const primaryMessage = Array.isArray(errorMessage)
          ? errorMessage[0]
          : errorMessage;
        // Specific error cases
        if (primaryMessage.includes("Your KYC hasn't been verified")) {
          handleToast("Your KYC hasn't been verified", "error");
          return;
        }
        // Attempt to parse JSON errors
        try {
          const parsed =
            typeof errorMessage === "string"
              ? JSON.parse(errorMessage)
              : errorMessage;

          if (parsed?.message) {
            handleToast(capitalizeFirstLetter(parsed.message), "error");
            return;
          }else{
             handleToast("Your KYC hasn't been verified", "error");
          }
        } catch (e) {
          // Not JSON - continue to fallback
        }
        // Final fallback
        showAddMoneyFailedModal(primaryMessage);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (inputValue) {
      getFee();
    }
  }, [inputValue]);
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Confirm details" navigation={navigation} />
        <ScrollView>
          <View style={styles.contentBody}>
            <View style={styles.detailWrap}>
              <DetailCard
                headText={"Money you’re adding"}
                amount={
                  <>
                    <P style={{ fontSize: 32, lineHeight: 48, marginRight: 2 }}>
                      ${formatToTwoDecimals(Number(fee?.amountInUSD)) || 0}
                    </P>
                    <P style={{ marginTop: 5 }}>USD</P>
                  </>
                }
                convertedAmount={
                  <>
                    <P style={{ fontSize: 16, lineHeight: 24, marginRight: 2 }}>
                      {symbol}
                      {formatNumberWithCommas(Number(fee?.totalAmountInLocal)) || 0}
                    </P>
                    <P style={{ marginTop: 2, fontSize: 12, lineHeight: 18 }}>
                      {currencyCode}
                    </P>
                  </>
                }
                bottomComponent={
                  <View style={styles.desCont}>
                    <View style={styles.items}>
                      <P style={styles.holder}>Fee</P>
                      <P style={styles.value}>
                        {formatNumberWithCommas(fee?.feeInLocal)}{" "}
                        <P
                          // @ts-ignore
                          style={[
                            styles.value,
                            { fontFamily: fonts.poppinsRegular },
                          ]}
                        >
                          {currencyCode}
                        </P>
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Exchange rate</P>
                      <P style={styles.value}>
                        1 USD ~ {`${ngnRate} ${currencyCode}`}
                      </P>
                    </View>
                    <View style={styles.items}>
                      <P style={styles.holder}>Payment method</P>
                      <P style={styles.value}>Bank transfer</P>
                    </View>
                    <View style={[styles.items, { marginBottom: 0 }]}>
                      <P style={styles.holder}>Processing time</P>
                      <P style={styles.value}>24 hours</P>
                    </View>
                  </View>
                }
              />
              <View style={styles.buttonWrap}>
                <Button
                  btnText="Confirm"
                  loading={loading}
                  onPress={() => {
                    if (country === "Turkey") {
                      navigation.navigate("BankNoticeScreen1", {
                        country: country,
                      });
                    } else {
                      // navigation.navigate("BankTransferScreen", {
                      //   country: country,
                      // });
                      deposit();
                    }
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.secBackground,
    paddingTop: 24,
  },
  detailWrap: {
    width: "90%",
    alignSelf: "center",
  },
  desCont: {
    width: "100%",
    marginTop: 8,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.dGray,
    fontFamily: fonts.poppinsRegular
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
});
