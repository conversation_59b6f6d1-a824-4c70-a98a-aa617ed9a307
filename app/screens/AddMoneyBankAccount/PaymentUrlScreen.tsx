import React, { useState, useRef } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
  BackHandler,
} from "react-native";
import { WebView } from "react-native-webview";
import { useFocusEffect } from "@react-navigation/native";
import { colors } from "../../config/colors";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import Div from "../../components/Div";
import { useCallback } from "react";

const { width, height } = Dimensions.get("window");

interface PaymentUrlScreenProps {
  navigation: any;
  route: {
    params: {
      url: string;
      title?: string;
      onSuccess?: () => void;
      onCancel?: () => void;
      onError?: (error: any) => void;
      transaction?: any;
    };
  };
}

export default function PaymentUrlScreen({ navigation, route }: PaymentUrlScreenProps) {
  const { url, title = "Payment", onSuccess, onCancel, onError, transaction } = route.params || {};
  const [loading, setLoading] = useState(true);
  const [canGoBack, setCanGoBack] = useState(false);
  const webViewRef = useRef<WebView>(null);

  // Handle hardware back button
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        if (canGoBack && webViewRef.current) {
          webViewRef.current.goBack();
          return true;
        }
        
        // Show confirmation dialog before closing
        Alert.alert(
          "Cancel Payment",
          "Are you sure you want to cancel this payment?",
          [
            {
              text: "Continue Payment",
              style: "cancel",
            },
            {
              text: "Cancel Payment",
              style: "destructive",
              onPress: () => {
                navigation.navigate("BottomTabNavigator")
              },
            },
          ]
        );
        return true;
      };

      const subscription = BackHandler.addEventListener("hardwareBackPress", onBackPress);
      return () => subscription.remove();
    }, [canGoBack, navigation, onCancel])
  );

  const handleNavigationStateChange = (navState: any) => {
    setCanGoBack(navState.canGoBack);
    
    // Check for success/failure URLs or patterns
    const currentUrl = navState.url.toLowerCase();
    console.log(currentUrl);

    if (currentUrl?.includes(transaction.payment?.redirectUrl)) {
      navigation.navigate("")
    }
    
    
    // Common success patterns
    if (
      currentUrl.includes("success") ||
      currentUrl.includes("completed") ||
      currentUrl.includes("approved") ||
      currentUrl.includes("payment-successful")
    ) {
      if (onSuccess) {
        onSuccess();
      }
      navigation.goBack();
      return;
    }
    
    // Common failure/cancel patterns
    if (
      currentUrl.includes("cancel") ||
      currentUrl.includes("failed") ||
      currentUrl.includes("error") ||
      currentUrl.includes("declined")
    ) {
      if (onCancel) {
        onCancel();
      }
      navigation.goBack();
      return;
    }
  };

  const handleError = (syntheticEvent: any) => {
    const { nativeEvent } = syntheticEvent;
    console.error("WebView error:", nativeEvent);
    
    if (onError) {
      onError(nativeEvent);
    }
    
    Alert.alert(
      "Payment Error",
      "There was an error loading the payment page. Please try again.",
      [
        {
          text: "Retry",
          onPress: () => {
            if (webViewRef.current) {
              webViewRef.current.reload();
            }
          },
        },
        {
          text: "Cancel",
          style: "cancel",
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  const handleLoadStart = () => {
    setLoading(true);
  };

  const handleLoadEnd = () => {
    setLoading(false);
  };

  if (!url) {
    Alert.alert("Error", "No payment URL provided", [
      { text: "OK", onPress: () => navigation.goBack() },
    ]);
    return null;
  }

  return (
    <View style={styles.container}>
      <Div>
        <AuthenticationHedear
          text={title}
          navigation={navigation}
          goHome={true}
          // onBackPress={() => {
          //   Alert.alert(
          //     "Cancel Payment",
          //     "Are you sure you want to cancel this payment?",
          //     [
          //       {
          //         text: "Continue Payment",
          //         style: "cancel",
          //       },
          //       {
          //         text: "Cancel Payment",
          //         style: "destructive",
          //         onPress: () => {
          //           if (onCancel) {
          //             onCancel();
          //           }
          //           navigation.goBack();
          //         },
          //       },
          //     ]
          //   );
          // }}
        />
        
        <View style={styles.webViewContainer}>
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          )}
          
          <WebView
            ref={webViewRef}
            source={{ uri: url }}
            style={styles.webView}
            onNavigationStateChange={handleNavigationStateChange}
            onError={handleError}
            onLoadStart={handleLoadStart}
            onLoadEnd={handleLoadEnd}
            startInLoadingState={true}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            allowsBackForwardNavigationGestures={true}
            scalesPageToFit={true}
            mixedContentMode="compatibility"
            userAgent="Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  webViewContainer: {
    flex: 1,
    position: "relative",
  },
  webView: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
    zIndex: 1,
  },
});
