import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import InputCard from "../../components/InputCard";
import Keyboard from "../../components/Keyboard";
import Button from "../../components/Button";
import { GetUserDetails } from "../../RequestHandlers/User";
import { GetUserWallet, GetRateByCountry } from "../../RequestHandlers/Wallet";
import { useToast } from "../../context/ToastContext";
import { countries } from "../../components/counties";
import BottomSheet from "../../components/BottomSheet";
import { formatToTwoDecimals } from "../../Utils/numberFormat";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");
export default function AmountScreen({ navigation, route }) {
  const { username, note } = route.params;
  const { details } = route?.params || {};
  const [inputValue, setInputValue] = useState("0");
  const [error, setError] = useState(false);
  // Remove isUsdInput since we'll always use selected currency
  const [teir, setTeir] = useState(0);
  const [fullname, setFullname] = useState("");
  const [usernameN, setUsername] = useState("");
  const [amount, setAmount] = useState(null);
  const [localRate, setLocalRate] = useState(0); // Local currency exchange rate
  const [showCurrencySelector, setShowCurrencySelector] = useState(false);
  const [balLoading, setBalLoading] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState({
    country: "United States",
    currency: "US Dollar",
    symbol: "$",
    currencyCode: "USD",
    code2: "US",
    flag: require("../../assets/usa.png"),
  }); // Default to USD
  const { handleToast } = useToast();

  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  const formatNumber = (value) => {
    value = value.toString();
    return value.replace(/[^0-9.]/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };



  // const convertedValue = formatNumber(Number(inputValue) * ngnRate);
  const handleKeyPress = (key) => {
    if (key === "←") {
      setInputValue((prev) => (prev.length > 1 ? prev.slice(0, -1) : "0"));
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      if (inputValue === "0" && key !== ".") {
        setInputValue(formatNumber(key));
      } else {
        setInputValue(formatNumber(inputValue + key));
      }
    }
  };
  // Remove toggle function since we always use selected currency

  const getUserDetails = async () => {
    const userDetails = await GetUserDetails();
    setFullname(`${userDetails.firstName} ${userDetails.lastName}`);
    setUsername(`${userDetails.username}`);
  };
  function ensureHttps(url) {
    if (url?.startsWith("http://")) {
      return url?.replace("http://", "https://");
    }
    return url; // Return the URL as is if it already starts with "https://"
  }

  const getUserWallet = async () => {
    try {
      const userWallet = await GetUserWallet();
      setAmount(userWallet?.totalInUsd);
    } catch (error) {}
  };

  const getLocalRate = async () => {
    try {
      // If USD is selected, set rate to 1 (no conversion needed)
      if (selectedCurrency.currencyCode === "USD") {
        setLocalRate(1);
        return;
      }

      const provider =
        selectedCurrency.currencyCode === "TRY" ? "tcmb" : "yellow-card";
      const sfxRate = await GetRateByCountry(
        selectedCurrency.currencyCode,
        provider
      );
      sfxRate.map((item: { type: string; amount: number }) => {
        if (item.type === "sell") {
          setLocalRate(item.amount);
        }
      });
    } catch (error) {
      console.error("Error fetching local rate:", error);
    }
  };

  const setSendAmount = () => {
    const inputAsNumber = Number(inputValue.replaceAll(",", ""));
    // Convert to USD: if selected currency is USD, no conversion needed
    const usdAmount = selectedCurrency.currencyCode === "USD" ? inputAsNumber : inputAsNumber / localRate;

    // Calculate minimum amount in selected currency
    const minimumAmountInSelectedCurrency = selectedCurrency.currencyCode === "USD" ? 1 : localRate;
    const formattedMinimumAmount = `${selectedCurrency.symbol}${formatToTwoDecimals(minimumAmountInSelectedCurrency)} ${selectedCurrency.currencyCode}`;

    if (inputValue === "0") {
      handleToast(`Minimum amount is ${formattedMinimumAmount}`, "error");
    } else if (usdAmount < 1) {
      setError(true);
      handleToast(`Minimum amount is ${formattedMinimumAmount}`, "error");
    } else if (usdAmount > amount) {
      handleToast("Insufficient balance", "error");
    } else if (teir === 1 && usdAmount > 3000) {
      setError(true);
      handleToast("Send money limit exceeded", "error");
    } else {
      
      navigation.navigate("SfxConfirmDetailsScreen", {
        amount: usdAmount, // Always pass USD amount
        username,
        note,
        details: details,
        inputAmount: inputAsNumber, // Pass the original input amount
        localRate: localRate,
        selectedCurrency: selectedCurrency,
      });
    }
  };
  const getUserTier = async () => {
    try {
      const teir = await GetUserDetails();
      setTeir(teir.tier.level);
    } catch (error) {
    } finally {
    }
  };

  useEffect(() => {
    getUserDetails();
    getUserWallet();
    getUserTier();
    getLocalRate();
  }, [selectedCurrency]);

  useEffect(() => {
    getUserDetails();
    getUserWallet();
    getUserTier();
  }, []);
  return (
    <View style={styles.body}>
      <Div>
        <ScrollView>
          <AuthenticationHedear text="Amount" navigation={navigation} />
          <View style={styles.contentBody}>
            <View
              style={{
                position: "absolute",
                width: (26 * width) / 100,
                top: (-5.5 * height) / 100,
                right: 24,
              }}
            >
              <TouchableOpacity
                style={styles.currencySelector}
                onPress={() => setShowCurrencySelector(true)}
              >
                <View style={styles.currencySelectorLeft}>
                  <Image
                    source={selectedCurrency.flag}
                    style={styles.currencySelectorFlag}
                  />
                  <P style={styles.currencySelectorText}>
                    {selectedCurrency.currencyCode}{" "}
                    {selectedCurrency.symbol}
                  </P>
                </View>
                <SvgXml xml={svg.arrowDown} width={16} height={16} />
              </TouchableOpacity>
            </View>
            <View style={styles.inputCardWrap}>
              <View
                style={{
                  width: "100%",
                  justifyContent: "center",
                  alignItems: "center",
                  marginBottom: 8,
                }}
              >
                <Image
                  source={
                    details.picture
                      ? { uri: ensureHttps(details.picture) }
                      : require("../../assets/face.png")
                  }
                  style={{ width: 40, height: 40, borderRadius: 100 }}
                />
              </View>
              <P style={{ textAlign: "center", fontSize: 12 }}>
                {details.firstName} {details.lastName}
              </P>
              <P
                style={{
                  color: "#A5A1A1",
                  textAlign: "center",
                  marginBottom: 16,
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                {details.username} | SFx money app
              </P>
              <InputCard
                headerText={"How much do you want to send"}
                // Remove toggle functionality
                amountValue={
                  <>
                    <P
                      numberOfLines={1}
                      style={{
                        textAlign: "center",
                        fontSize: 32,
                        lineHeight: 48,
                        marginRight: 4,
                      }}
                    >
                      {`${selectedCurrency.symbol}${formatNumber(inputValue)}`}
                      <P
                        style={{
                          lineHeight: 48,
                          fontSize: 12,
                          fontFamily: fonts.poppinsRegular,
                        }}
                      >
                        {selectedCurrency.currencyCode}
                      </P>
                    </P>
                  </>
                }
                // Remove converted value display
                // text1={
                //   selectedCurrency.currencyCode !== "USD"
                //     ? `Exchange rate: 1 USD ~ ${localRate} ${selectedCurrency.currencyCode}`
                //     : "Base currency: USD"
                // }
                text2={`Available balance: ${
                  amount ? formatToTwoDecimals(Number(amount)) : ""
                }`}
                error={error}
                loading={balLoading}
              />
            </View>
            <View style={styles.bottom}>
              <View style={{ width: "90%", alignSelf: "center" }}>
                <Keyboard onKeyPress={handleKeyPress} />
              </View>
              <View
                style={{ width: "80%", alignSelf: "center", marginTop: 16 }}
              >
                <Button
                  btnText="Next"
                  onPress={() => {
                    setSendAmount();
                  }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>

      {/* Currency Selection Bottom Sheet */}
      <BottomSheet
        isVisible={showCurrencySelector}
        onClose={() => setShowCurrencySelector(false)}
        backspaceText="Select Local Currency"
        modalContentStyle={{ height: "75%" }}
        extraModalStyle={{ height: "73%" }}
        components={
          <ScrollView
            contentContainerStyle={{ paddingBottom: 300, paddingTop: 24 }}
            showsVerticalScrollIndicator={false}
          >
            {countries.map((country, index) => (
              <TouchableOpacity
                key={index}
                style={styles.currencyItem}
                onPress={() => {
                  setSelectedCurrency({
                    country: country.country,
                    currency: country.currency,
                    symbol: country.symbol,
                    currencyCode: country.currencyCode,
                    code2: country.code2,
                    flag: country.flag,
                  });
                  setShowCurrencySelector(false);
                  setInputValue("0"); // Reset input when changing currency
                }}
              >
                <View style={styles.currencyItemContent}>
                  <Image source={country.flag} style={styles.flagImage} />
                  <View style={styles.currencyInfo}>
                    <P style={styles.countryName}>{country.country}</P>
                    <P style={styles.currencyName}>
                      {country.currency} ({country.currencyCode})
                    </P>
                  </View>
                  <P style={styles.currencySymbol}>{country.symbol}</P>
                </View>
                {selectedCurrency.currencyCode === country.currencyCode && (
                  <SvgXml xml={svg.checked} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    // backgroundColor: "#fff",
    backgroundColor: "rgba(247, 244, 255, 1)",
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  btnCard: {
    width: "90%",
    minHeight: 156,
    backgroundColor: "white",
    alignSelf: "center",
    marginTop: 24,
    borderRadius: 12,
    paddingTop: 16,
    paddingBottom: 16,
    // paddingLeft: 16,
    // paddingRight: 16,
  },
  btnSec1: {
    width: "100%",
    justifyContent: "space-around",
    flexDirection: "row",
    marginBottom: 24,
    // paddingHorizontal: 18.33
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
  },
  bottom: {
    width,
    flex: 1,
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.12),
    marginTop: 16,
  },
  currencySelector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: colors.secBackground,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  currencySelectorLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  currencySelectorFlag: {
    width: 16,
    height: 16,
    borderRadius: 10,
    marginRight: 4,
  },
  currencySelectorText: {
    fontSize: 10,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  currencyItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  currencyItemContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    flex: 1,
  },
  flagImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
  currencyInfo: {
    flex: 1,
  },
  countryName: {
    fontSize: 14,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
  },
  currencyName: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
    marginTop: 2,
  },
  currencySymbol: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    color: colors.black,
    marginRight: 12,
  },
  selectedIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
});
