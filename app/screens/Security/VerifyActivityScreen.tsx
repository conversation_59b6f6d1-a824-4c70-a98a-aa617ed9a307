import React, { useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  Text,
  TouchableOpacity,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { colors } from "../../config/colors";
import Button from "../../components/Button";
import P from "../../components/P";
import Keyboard from "../../components/Keyboard";
import NoteComponent2 from "../../components/NoteComponent2";
import { SendOtp } from "../../RequestHandlers/Authentication";
import { GetUserDetails } from "../../RequestHandlers/User";
import { VerifyOtp } from "../../RequestHandlers/Authentication";
import { useToast } from "../../context/ToastContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");

export default function VerifyActivityScreen({ navigation, route }) {
  const [fields, setFields] = useState(["", "", "", "", ""]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showDots, setShowDots] = useState([false, false, false, false, false]);
  const [error, setError] = useState(false); // Add error state
  const [secondsLeft, setSecondsLeft] = useState(60); // Set countdown time to 1 minute
  const [isCountdownActive, setIsCountdownActive] = useState(true); // To track if countdown is active
  const refs = [useRef(), useRef(), useRef(), useRef(), useRef];
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [code, setCode] = useState("");
  const { activityId, activityType } = route?.params || "";
  const { ActivityFunction } = route?.params || "";
  const [is2faEnabled, setIs2faEnabled] = useState(false);
  const { handleToast } = useToast();

  // Countdown logic
  useEffect(() => {
    if (isCountdownActive && secondsLeft > 0) {
      const interval = setInterval(() => {
        setSecondsLeft((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else if (secondsLeft === 0) {
      setIsCountdownActive(false);
    }
  }, [secondsLeft, isCountdownActive]);

  const handleKeyPress = (key) => {
    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 4) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };

  const handleChangeText = (index, text) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });
        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);
      }

      return updatedFields;
    });
  };
  const verifyOtp = async () => {
    setLoading(true);
    try {
      const body = {
        otp: Number(code),
        email: email,
        type: "verify-activity",
        activityType: activityType,
        activityId: activityId,
      };
      const verifyOtp = await withApiErrorToast(VerifyOtp(body), handleToast);
      if (verifyOtp.error) {
        handleToast(verifyOtp.message, "error");
      } else {
        if (is2faEnabled) {
          navigation.navigate("TwofactorAuthScreen2", {
            ActivityFunction: ActivityFunction,
            activityId: activityId,
          });
        } else {
          ActivityFunction();
        }
      }
    } catch (error) {
      setLoading(false);
      handleToast("Unknown error", "error");
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 5000);
    }
  };

  const handleSubmit = () => {
    // Check if any field is empty
    if (fields.some((field) => field === "")) {
      setError(true);
    } else {
      setError(false);
      verifyOtp();
    }
  };
  const getUserdetails = async () => {
    try {
      const details = await GetUserDetails();
      setEmail(details.email);
      if (details.email) {
        getOtp(details.email);
        setIs2faEnabled(details?._2faEnabled);
      }
    } catch (error) { }
  };

  const getOtp = async (email) => {
    try {
      const body = {
        email: email,
        type: "verify-activity",
      };
      const resendOtp = await SendOtp(body);
    } catch (error) {
      handleToast("Network error", "error");
    }
  };
  useEffect(() => {
    getUserdetails();
  }, []);

  // Handle OTP resend
  const handleResendOtp = () => {
    setSecondsLeft(60);
    setIsCountdownActive(true);
    if (email != "") {
      getOtp(email);
    }
  };

  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      setCode(fields.join(""));
    }
  }, [fields]);

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear
          text="Email verification"
          navigation={navigation}
          goHome={true}
        />
        <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.contentBody}>
            <View style={styles.inputCardWrap}>
              <P
                style={{
                  color: colors.gray,
                  textAlign: "center",
                  marginBottom: 16,
                  fontSize: 12,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Enter Verification code
              </P>
              <View style={styles.con}>
                {refs.map((ref, index) => (
                  <View
                    key={index}
                    style={[
                      styles.pinInput,
                      {
                        marginRight: index === refs.length - 1 ? 0 : 16,
                        borderColor:
                          activeIndex === index
                            ? colors.primary
                            : error && fields[index] === ""
                              ? "red" // Highlight empty fields with red border
                              : "#E6E5E5",
                      },
                    ]}
                  >
                    <View style={styles.pinView}>
                      {showDots[index] ? (
                        <View style={styles.dot} />
                      ) : (
                        <Text style={styles.pinText}>{fields[index]}</Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>

              {/* Error message */}
              {error && (
                <Text style={styles.errorText}>Please fill in all fields.</Text>
              )}

              {/* Countdown Timer or Resend OTP */}
              <View
                style={{
                  // width: 116,
                  height: 24,
                  backgroundColor: "#F7F4FF",
                  justifyContent: "center",
                  alignItems: "center",
                  borderRadius: 1000,
                  flexDirection: "row",
                  marginBottom: 20,
                  paddingLeft: 20,
                  paddingRight: 20,
                }}
              >
                {isCountdownActive ? (
                  <>
                    <P
                      style={{ fontSize: 10, fontFamily: fonts.poppinsMedium }}
                    >
                      Expires in{" "}
                    </P>
                    <P
                      style={{
                        fontSize: 10,
                        fontFamily: fonts.poppinsMedium,
                        color: "#8B52FF",
                      }}
                    >
                      {` ${secondsLeft} seconds`}
                    </P>
                  </>
                ) : (
                  <TouchableOpacity onPress={handleResendOtp}>
                    <P
                      style={{
                        fontSize: 10,
                        fontFamily: fonts.poppinsMedium,
                        color: colors.red,
                      }}
                    >
                      Resend OTP
                    </P>
                  </TouchableOpacity>
                )}
              </View>
            </View>

            <View
              style={{
                width: "90%",
                alignSelf: "center",
                marginTop: (16 / baseHeight) * height,
              }}
            >
              <NoteComponent2
                text={
                  "To complete this activity, please verify your identity using the verification code sent to your email"
                }
              />
            </View>
            <View style={styles.bottom}>
              <Keyboard onKeyPress={handleKeyPress} />
              <View
                style={{ width: "80%", marginTop: 32, alignSelf: "center" }}
              >
                <Button
                  btnText="Confirm"
                  onPress={handleSubmit}
                  loading={loading}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    marginTop: -16,
    alignItems: "center",
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    padding: (24 / baseHeight) * height,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 48,
    height: 48,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16, // Bigger size for the dot
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "space-around",
    // width: "80%",
    // backgroundColor: "red",
    // marginBottom: 32,
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    marginTop: 8,
    marginBottom: 10,
  },
  bottom: {
    width: "90%",
    flex: 1,
    alignSelf: "center",
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.15),
    marginTop: 20,
  },
});
