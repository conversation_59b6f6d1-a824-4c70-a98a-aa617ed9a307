import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  Animated,
  RefreshControl,
} from "react-native";
import { colors } from "../../config/colors";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import VirtualCard from "../../components/VirtualCard";
import TransactionItem from "../../components/TransactionItem";
import { GetUserDetails } from "../../RequestHandlers/User";
import {
  GetcardById,
  GetcardByToken,
  Getcards,
} from "../../RequestHandlers/Card";
import Loader from "../../components/ActivityIndicator";
import { ActivityIndicator } from "react-native";
import TextContentLoader from "../../components/TextContentLoader";
import {
  GetCardTransactions,
  GetTransation,
} from "../../RequestHandlers/Wallet";
import { formatDate } from "../../components/FormatDate";
import FailedToLoad from "../../components/ErrorSate/FailedToLoad";
import { useToast } from "../../context/ToastContext";
import { useFocusEffect } from "@react-navigation/native";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");
const SRC_WIDTH = Dimensions.get("window").width;
const cardLength = (327 / 375) * SRC_WIDTH;
const spacing = SRC_WIDTH * 0.03;
export default function CardScreen({ navigation }) {
  const [hideBal, setHideBal] = useState(false);
  const [amount, setAmount] = useState("0.00");
  const [isCardPinCreated, setIsCardPinCreated] = useState(true);
  const [cardColor, setCardColor] = useState("brown");
  const [activeIndex, setActivendex] = useState(0);
  const [cardDetails, setCardDetails] = useState<any>([]);
  const [loader, setLoader] = useState(false);
  const [exYear, setExYear] = useState("");
  const [cardList, setCardList] = useState<any>([]);
  const [loader1, setLoader1] = useState(false);
  const [transactions, setTransations] = useState([]);
  const [isGrayscale, setIsGrayscale] = useState(false);
  const [details, setDetails] = useState<any>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [isCardFreezed, setIsCardFreezed] = useState(false);
  const [isCardInReview, setIsCardInreview] = useState(false);
  const [isDataLaodFailed, setIsDataLoadFailed] = useState(false);
  const { handleToast } = useToast();
  const onRefresh = () => {
    setRefreshing(true);
    getCards();
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };
  const getCards = async () => {
    try {
      const card = await Getcards();
      if (card.length > 0) {
        getCardById(card[activeIndex]?.id);
        setCardList(card);
      }
      if (card.error) {
        setIsDataLoadFailed(true);
      } else {
        setIsDataLoadFailed(false);
      }
    } catch (error) {}
  };

  const getCardById = async (id) => {
    try {
      const cardDetails = await GetcardById(id);
      getTransaction(id);
      if (cardDetails.details) {
        setDetails(cardDetails.details);
        setAmount(cardDetails.details.balance);
        const yyyy = cardDetails.details.expiry_year;
        const lastyyyy = yyyy.split("");
        setExYear(`${lastyyyy[2]}${lastyyyy[3]}`);
      }
      if (cardDetails?.card) {
        setCardDetails(cardDetails);
        setIsCardFreezed(cardDetails?.card?.status === "active" ? false : true);
      }
    } catch (error) {
    } finally {
      setLoader(false);
      setLoader1(false);
    }
  };
  const getUserDetails = async () => {
    try {
      const res = await GetUserDetails();
      if (res.homeCountry) {
        setIsCardInreview(res.bridgeCardflagForManualReview);
      }
    } catch (error) {}
  };
  // const getCardByToken = async (token) => {
  //   try {
  //     const cardDetails = await GetcardByToken(token);
  //     if (cardDetails.data) {
  //       setDetails(cardDetails.data);
  //       setAmount(cardDetails.data.balance);
  //       const yyyy = cardDetails.data.expiry_year;
  //       const lastyyyy = yyyy.split("");
  //       setExYear(`${lastyyyy[2]}${lastyyyy[3]}`);
  //     }
  //   } catch (error) {
  //   } finally {
  //     setLoader(false);
  //     setLoader1(false);
  //   }
  // };
  const getTransaction = async (id) => {
    setLoader1(true);
    try {
      const transactions = await GetCardTransactions(1, 10, id);
      if (transactions) {
        setLoader1(false);
      }
      const sortedTransactions = transactions.items.sort((a, b) => {
        return (
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );
      });
      setTransations(sortedTransactions);
      if (transactions.error) {
        handleToast(transactions.message, "error");
      }
    } catch (error) {
    } finally {
      setLoader1(false);
    }
  };
  function formatCardNumber(cardNumber) {
    const cleaned = cardNumber.replace(/\D+/g, "");
    const formatted = cleaned.match(/.{1,4}/g)?.join(" ") || "";
    return formatted;
  }
  useEffect(() => {
    setLoader1(true);
    getCards();
  }, [activeIndex]);
  useFocusEffect(
    useCallback(() => {
      getCards();
      getUserDetails();
    }, [])
  );
  useEffect(() => {
    setLoader(true);
  }, []);
  const scrollX = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef(null);
  const dummyArr = [1];
  function capitalizeFirstLetter(word) {
    if (!word) return ""; // handle empty strings
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  }
  useEffect(() => {
    scrollX.addListener(({ value }) => {
      const newIndex = Math.round(value / (cardLength + spacing));
      setActivendex(newIndex);
    });
    return () => {
      scrollX.removeAllListeners();
    };
  }, [scrollX]);

  const renderItem = ({ item, index }) => {
    return (
      <View style={{ marginHorizontal: spacing / 2 }}>
        <View style={styles.cc}>
          <VirtualCard
            isCardFreezed={item?.status == "active" ? false : true}
            ccStyle={{ height: (188 / baseHeight) * height }}
            contStyle={{ marginTop: 0, marginBottom: 0, width: "100%" }}
            cvv={!loader1 && details?.cvv ? details.cvv : "..."}
            name={
              details.card_name && !loader1
                ? details.card_name?.toUpperCase()
                : "....."
            }
            cardNumber={
              details.card_number && !loader1
                ? formatCardNumber(details.card_number)
                : ".... .... .... ...."
            }
            CardText1={{ fontSize: 9 }}
            CardText2={{ fontSize: 15 }}
            cardColor={item?.colour}
            validDate={`${
              cardDetails?.details && !loader1 ? details.expiry_month : ".."
            }/${cardDetails?.details && !loader1 ? exYear : ".."}`}
          />
        </View>
      </View>
    );
  };
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="Card" navigation={navigation} />
        <ScrollView
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {isDataLaodFailed ? (
            <>
              <FailedToLoad
                onPress={() => {
                  setLoader(true);
                  getCards();
                }}
              />
            </>
          ) : (
            <>
              <View style={[styles.card]}>
                <View style={styles.accountBalance}>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <P style={{ fontSize: 12 }}>Available card balance</P>
                    <TouchableOpacity
                      style={{
                        marginLeft: 8,
                        width: 20,
                        height: 20,
                        justifyContent: "center",
                      }}
                      onPress={() => setHideBal((prevState) => !prevState)} // Toggle state
                    >
                      <SvgXml
                        xml={hideBal === true ? svg.eyeClose : svg.eyeOpen}
                      />
                      {/* Switch icon */}
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      width: "100%",
                      alignItems: "center",
                      marginTop: "2%",
                    }}
                  >
                    {/* @ts-ignore */}
                    <TouchableOpacity
                    //  onPress={() => toggleModal()}
                    >
                      <View
                        style={{ flexDirection: "row", alignItems: "center" }}
                      >
                        {loader1 ? (
                          <View style={{ width: 200 }}>
                            <TextContentLoader />
                          </View>
                        ) : (
                          // <></>
                          <P
                            style={{
                              fontSize: 24,
                              lineHeight: 36,
                              color: isCardFreezed ? colors.gray : colors.black,
                            }}
                          >
                            {hideBal
                              ? "******"
                              : `$${
                                  (Number(amount) / 100)?.toFixed(2) || 0
                                }`}{" "}
                            <Text
                              style={{
                                fontSize: 16,
                                fontFamily: fonts.poppinsMedium,
                                color: isCardFreezed
                                  ? colors.gray
                                  : colors.black,
                              }}
                            >
                              {hideBal ? "***" : "USD"}
                            </Text>
                          </P>
                        )}
                        {/* <SvgXml xml={svg.arrowDown} style={{ marginLeft: 8 }} /> */}
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.addMoneyButton}
                      onPress={() => {
                        if (isCardInReview) {
                          handleToast("You have a card in review", "error");
                        } else {
                          navigation.navigate("CardApplicationScreen");
                        }
                      }}
                    >
                      <P style={styles.addMoneyText}>Add new card</P>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              {cardList.length === 1 ? (
                <View
                  style={{
                    // backgroundColor: "red",
                    width: "90%",
                    alignSelf: "center",
                    alignItems: "center",
                  }}
                >
                  <VirtualCard
                    ccStyle={{ height: (188 / baseHeight) * height }}
                    contStyle={{ marginTop: 0, marginBottom: 0 }}
                    name={details.card_name?.toUpperCase()}
                    cardNumber={
                      details.card_number
                        ? formatCardNumber(details.card_number)
                        : "1234 1234 1234 1234"
                    }
                    CardText1={{ fontSize: 9 }}
                    CardText2={{ fontSize: 15 }}
                    cardColor={cardDetails?.card?.colour}
                    cvv={details?.cvv ? details.cvv : "..."}
                    validDate={`${details.expiry_month}/${exYear}`}
                  />
                </View>
              ) : (
                <View style={{}}>
                  <Animated.FlatList
                    data={cardList}
                    ref={flatListRef}
                    keyExtractor={(item, index) => index.toString()}
                    renderItem={renderItem}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    snapToInterval={cardLength + spacing}
                    decelerationRate="fast"
                    bounces={false}
                    onScroll={Animated.event(
                      [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                      { useNativeDriver: false }
                    )}
                    contentContainerStyle={{ paddingHorizontal: 23 }}
                  />
                </View>
              )}
              {cardList?.length > 0 && (
                <View style={styles.dotsContainer}>
                  {cardList.map((_, i) => {
                    const opacity = scrollX.interpolate({
                      inputRange: [
                        (i - 1) * (cardLength + spacing),
                        i * (cardLength + spacing),
                        (i + 1) * (cardLength + spacing),
                      ],
                      outputRange: [0.3, 1, 0.3],
                      extrapolate: "clamp",
                    });
                    return (
                      <Animated.View
                        key={i}
                        style={[styles.dot, { opacity }]}
                      />
                    );
                  })}
                </View>
              )}
              {!isCardPinCreated && (
                <TouchableOpacity
                  onPress={() => navigation.navigate("CreateCardPin")}
                >
                  <View
                    style={[
                      styles.card,
                      {
                        backgroundColor: "rgba(238, 255, 235, 1)",
                        marginBottom: 0,
                        marginTop: 24,
                      },
                    ]}
                  >
                    <View style={styles.recommendation}>
                      <View>
                        <P style={styles.recommendationTitle}>
                          Personalized recommendation
                        </P>
                        <P style={styles.recommendationMessage}>
                          Create your card transaction PIN
                        </P>
                      </View>
                      <Image
                        style={styles.recommendationImage}
                        source={require("../../assets/lock.png")}
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              )}

              {/* transactions */}
              {loader1 ? (
                <View
                  style={[
                    styles.card,
                    {
                      height: 79,
                      backgroundColor: colors.white,
                      borderRadius: 12,
                      marginTop: 24,
                      marginBottom: 24,
                    },
                  ]}
                >
                  <ActivityIndicator size="large" color={colors.primary} />
                </View>
              ) : (
                <View
                  style={[
                    styles.card,
                    {
                      height: 79,
                      backgroundColor: colors.white,
                      borderRadius: 12,
                      marginTop: 24,
                      marginBottom: 24,
                    },
                  ]}
                >
                  <View style={styles.actions}>
                    <ActionButton
                      text="Send"
                      isCardFreezed={isCardFreezed}
                      svg={svg.withdraw}
                      onPress={() =>
                        navigation.navigate("CardSendScreen", {
                          details: cardDetails,
                          details2: details,
                        })
                      }
                    />
                    <ActionButton
                      text="Top-up"
                      svg={svg.ccaddFill}
                      isCardFreezed={isCardFreezed}
                      onPress={() =>
                        navigation.navigate("CardTopUp", {
                          details: cardDetails,
                          details2: details,
                        })
                      }
                    />
                    <ActionButton
                      text="Billing"
                      svg={svg.billing}
                      isCardFreezed={isCardFreezed}
                      onPress={() =>
                        navigation.navigate("BillingAddressScreen", {
                          details: cardDetails,
                          details2: details,
                        })
                      }
                    />
                    <ActionButton
                      text="More"
                      svg={svg.more}
                      isCardFreezed={false}
                      onPress={() =>
                        navigation.navigate("CardManagementScreen", {
                          details: cardDetails,
                          details2: details,
                        })
                      }
                    />
                  </View>
                </View>
              )}
              {loader1 ? (
                <View
                  style={[
                    styles.card,
                    {
                      padding: 0,
                      minHeight: 246,
                      marginBottom: 100,
                      backgroundColor: colors.white,
                      alignItems: "center",
                      justifyContent: "center",
                    },
                  ]}
                >
                  <ActivityIndicator size="large" color={colors.primary} />
                </View>
              ) : (
                <View
                  style={[
                    styles.card,
                    {
                      padding: 0,
                      minHeight: 246,
                      marginBottom: 100,
                      backgroundColor: colors.white,
                    },
                  ]}
                >
                  <View
                    style={{
                      width: "100%",
                      borderBottomWidth: 1,
                      borderColor: "rgba(240, 239, 239, 1)",
                      flexDirection: "row",
                      justifyContent: "space-between",
                      height: 45,
                      alignItems: "center",
                      paddingHorizontal: 16,
                    }}
                  >
                    <P
                      style={{ color: "rgba(165, 161, 161, 1)", fontSize: 14 }}
                    >
                      Recent transaction
                    </P>
                    {transactions.length > 4 && (
                      <TouchableOpacity
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                        onPress={() => {
                          navigation.navigate("CardHistory", {
                            id: cardDetails?.card?.id,
                          });
                        }}
                      >
                        <P
                          style={{
                            fontSize: 14,
                            marginRight: 5,
                            color: colors.primary,
                            textDecorationLine: "underline",
                          }}
                        >
                          View all
                        </P>
                        <SvgXml xml={svg.arroveRight} />
                      </TouchableOpacity>
                    )}
                  </View>

                  <View style={[]}>
                    {cardDetails?.card?.transactions?.length === 0 ? (
                      <View style={{ width: "100%", alignItems: "center" }}>
                        <SvgXml
                          xml={svg.walletFace}
                          style={{ marginTop: 52, marginBottom: 16 }}
                        />
                        <P
                          style={{
                            fontFamily: fonts.poppinsMedium,
                            marginBottom: 4,
                          }}
                        >
                          No transaction!
                        </P>
                        <P
                          style={{
                            color: "#A5A1A1",
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          You have no transaction yet
                        </P>
                      </View>
                    ) : (
                      transactions?.slice(0, 4)?.map((item, index) => (
                        <TransactionItem
                          key={index}
                          onPress={() => {
                            if (item?.status != "completed") {
                              if (item?.type === "fund-card") {
                                navigation.navigate("CardTopStatus", {
                                  response: item,
                                });
                              } else if (item?.type === "withdraw-from-card") {
                                navigation.navigate("CardSendStatus", {
                                  response: item,
                                });
                              } else {
                                navigation.navigate("CardTransactionDetails", {
                                  id: item.id,
                                });
                              }
                            } else {
                              navigation.navigate("CardTransactionDetails", {
                                id: item.id,
                              });
                            }
                          }}
                          icon={
                            item?.type === "fund-card"
                              ? svg.ccaddFill
                              : item?.type === "withdraw-from-card"
                              ? svg.withdraw
                              : item?.type === "create-card"
                              ? svg.cardF
                              : item?.type === "card-debit"
                              ? svg.bb
                              : item?.type === "card-transaction-reversal"
                              ? svg.bb
                              : svg.bankGreen
                          }
                          itemStyle={{
                            borderColor: colors.stroke,
                            alignItems: "center",
                            paddingLeft: 16,
                            paddingRight: 16,
                          }}
                          amount={`${
                            item?.type === "fund-card" ||
                            item?.type === "create-card"
                              ? "+"
                              : "-"
                          }$${
                            item?.amount
                              ? item?.amount?.toFixed(2).toLocaleString()
                              : "..."
                          }`}
                          title={
                            item?.type === "fund-card"
                              ? "Card top-up"
                              : item?.type === "withdraw-from-card"
                              ? "Card withdrawal"
                              : item?.type === "create-card"
                              ? "Card payment"
                              : item?.type === "card-transaction-reversal"
                              ? "Reversal"
                              : "Spent USD"
                          }
                          date={formatDate(item?.updatedAt)}
                          status={
                            item.status === "completed"
                              ? "Successful"
                              : item?.status === "processing"
                              ? "Pending"
                              : capitalizeFirstLetter(item?.status)
                          }
                          statusStyle={{
                            textAlign: "right",
                            color: item.status
                              .toLowerCase()
                              .includes("completed")
                              ? colors.green
                              : item.status.toLowerCase().includes("pending") ||
                                item.status.toLowerCase().includes("processing")
                              ? colors.yellow
                              : colors.red,
                          }}
                        />
                      ))
                    )}
                  </View>
                </View>
              )}
            </>
          )}
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

function BillItem({ text, svg, onPress }: any) {
  return (
    <TouchableOpacity onPress={onPress}>
      <View style={styles.billItem}>
        <SvgXml xml={svg} />
        <P style={styles.billText}>{text}</P>
      </View>
    </TouchableOpacity>
  );
}

function ActionButton({ text, svg, onPress = () => {}, isCardFreezed }) {
  return (
    <TouchableOpacity
      style={[styles.actionButton, { opacity: isCardFreezed ? 0.5 : 1 }]}
      onPress={onPress}
      disabled={isCardFreezed ? true : false}
    >
      <SvgXml xml={svg} />
      <P style={styles.actionButtonText}>{text}</P>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  card: {
    // backgroundColor: 'red',¿
    borderRadius: 12,
    padding: 16,
    width: "90%",
    alignSelf: "center",
    marginBottom: 12,
    // elevation: 4,
  },
  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  addMoneyButton: {
    // marginTop: 10,
    // paddingVertical: 8,
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "rgba(140, 82, 255, 1)",
    borderRadius: 20,
  },
  addMoneyText: {
    color: "#fff",
    fontSize: 12,
  },
  cc: {
    width: cardLength,
    // marginHorizontal: spacing / 2,
    // alignSelf: "center",
    // alignItems: "center",
  },
  recommendation: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  recommendationImage: {
    width: 48,
    height: 48,
    // backgroundColor: "gray",
  },
  recommendationTitle: {
    fontSize: 14,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  recommendationMessage: {
    fontSize: 12,
    color: "rgba(78, 130, 76, 1)",
    width: 218,
    // paddingRight: 40,
  },
  bills: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  billItem: {
    alignItems: "center",
  },
  billIcon: {
    width: 40,
    height: 40,
    backgroundColor: "gray",
    borderRadius: 20,
  },
  billText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-evenly",
  },
  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  transactionItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 20,
    justifyContent: "space-between",
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    paddingHorizontal: 16,
  },
  transactionFlag: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "gray",
  },
  transactionInfo: {
    marginLeft: 10,
  },
  transactionAmount: {
    fontSize: 16,
    fontFamily: "poppins-semibold",
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  emptyCont: {
    width: "100%",
    height: 204,
    alignItems: "center",
    justifyContent: "center",
  },
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: (14 / baseHeight) * height,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 5,
    backgroundColor: colors.primary,
    marginRight: 8,
  },
});
