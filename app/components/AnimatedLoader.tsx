import React, { useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Dimensions,
  Modal,
  Animated,
  Easing,
  Text,
  TouchableOpacity,
} from 'react-native';
import { colors } from '../config/colors';
import { SvgXml } from 'react-native-svg';
import { svg } from '../config/Svg';
import { fonts } from '../config/Fonts';

const { width, height } = Dimensions.get('window');

interface AnimatedLoaderProps {
  visible?: boolean;
  loading?: boolean;
  showRetry?: boolean;
  onRetry?: () => void;
  message?: string;
}

const AnimatedLoader: React.FC<AnimatedLoaderProps> = ({
  visible = false,
  loading = true,
  showRetry = false,
  onRetry,
  message = '',
}) => {
  // Animation values
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Start animations when component mounts and loading is true
  useEffect(() => {
    if (loading && !showRetry && visible) {
      // Fade in animation
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Rotation animation - continuous 360 degree rotation
      Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      ).start();

      // Pulse animation - subtle size pulsing
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 0.95,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Scale animation - subtle bounce effect
      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.05,
            duration: 500,
            easing: Easing.out(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0.95,
            duration: 500,
            easing: Easing.in(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();
    }

    return () => {
      // Reset animations when component unmounts
      rotateAnim.setValue(0);
      scaleAnim.setValue(1);
      opacityAnim.setValue(0);
      pulseAnim.setValue(1);
    };
  }, [loading, showRetry, visible, rotateAnim, scaleAnim, opacityAnim, pulseAnim]);

  // Interpolate rotation value to create a rotation string
  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Modal
      transparent={true}
      visible={visible}
      statusBarTranslucent={true}
      animationType="fade"
    >
      <View style={styles.container}>
        {showRetry ? (
          <View style={styles.retryContainer}>
            <Text style={styles.retryText}>
              Request timed out. Please try again.
            </Text>
            <TouchableOpacity onPress={onRetry} style={styles.retryButton}>
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.loaderContainer}>
            <Animated.View
              style={[
                styles.logoContainer,
                {
                  opacity: opacityAnim,
                  transform: [
                    { rotate: spin },
                    { scale: scaleAnim }
                  ],
                },
              ]}
            >
              {/* Outer pulsing circle */}
              <Animated.View
                style={[
                  styles.pulseCircle,
                  {
                    transform: [{ scale: pulseAnim }],
                  },
                ]}
              />

              {/* Logo in the center */}
              <View style={styles.logoWrapper}>
                <SvgXml xml={svg.loaderLogo} width={30} height={30} />
              </View>
            </Animated.View>

            {message ? (
              <Animated.Text
                style={[
                  styles.loadingText,
                  { opacity: opacityAnim }
                ]}
              >
                {message}
              </Animated.Text>
            ) : null}
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: 'rgba(255, 255, 255, 0.9)',
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    width: 120,
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  logoWrapper: {
    width: 60,
    height: 60,
    borderRadius: 100,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    zIndex: 2,
  },
  pulseCircle: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: colors.primary,
    opacity: 0.5,
    zIndex: 1,
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    color: colors.dGray,
    textAlign: "center",
    fontFamily: fonts.poppinsMedium
  },
  retryContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.white,
    borderRadius: 10,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  retryText: {
    fontSize: 16,
    color: colors.primary,
    marginBottom: 15,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  retryButtonText: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default AnimatedLoader;
