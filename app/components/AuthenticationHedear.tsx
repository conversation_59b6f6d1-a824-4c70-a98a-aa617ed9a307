import {
  Dimensions,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ViewStyle,
  TextInput,
  Animated,
} from "react-native";
import React, { CSSProperties, useEffect, useState, useRef } from "react";
import { Feather } from "@expo/vector-icons";
import P from "./P";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import { colors } from "../config/colors";

const { width, height } = Dimensions.get("window");

interface PProps {
  navigation?: any;
  text?: string;
  iconComp?: any;
  contStyle?: ViewStyle;
  navStyle?: CSSProperties;
  showBorder?: boolean;
  showBackArrow?: boolean;
  goHome?: boolean;
  cancel?: boolean;
  modalClose?: () => void; // Updated type for modalClose
  disabled?: boolean;
  goToScreen?: string;
  type?: string;
  submitFunction?: () => void;
  currentStep?: number;
  loading?: boolean;
  showSubmit?: boolean;
  search?: boolean;
  onSearchChange?: (text: string) => void;
  searchValue?: string;
  onClearPress?: () => void;
}

export default function AuthenticationHeader({
  navigation,
  text,
  iconComp,
  contStyle,
  navStyle,
  showBorder,
  showBackArrow = true,
  goHome = false,
  cancel,
  disabled,
  modalClose,
  goToScreen,
  type,
  submitFunction,
  currentStep,
  loading = false,
  showSubmit = true,
  search = false,
  onSearchChange,
  searchValue,
  onClearPress,
}: PProps) {
  const handlePress = () => {
    if (cancel && modalClose) {
      modalClose(); // Call the modalClose function
    } else if (goHome) {
      navigation.reset({
        index: 0,
        routes: [{ name: "BottomTabNavigator" }],
      });
    } else if (goToScreen) {
      navigation.navigate(goToScreen);
    } else {
      navigation.pop();
    }
  };
  const [activeDot, setActiveDot] = useState(0);
  // Create an array of numbers [1, 2, 3] for the dots
  const dotsArray = [1, 2, 3];
  const [expandSearch, setExpandSearch] = useState(false);

  // Animation values
  const searchAnimation = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(1)).current;

  // Cycle through the dots every 500ms
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length); // Cycle through dots
      }, 200);
      return () => clearInterval(interval);
    }
  }, [loading]);

  return (
    <>
      {type === "KYC" ? (
        // @ts-ignore
        <View
          style={[{ width, alignItems: "center", marginTop: 16 }, contStyle]}
        >
          <View
            style={{
              width: "90%",
              justifyContent: "space-between",
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <TouchableOpacity
              onPress={() => {
                navigation.goBack();
              }}
            >
              <SvgXml xml={svg.arrowLeft} />
            </TouchableOpacity>
            {showSubmit && (
              <TouchableOpacity
                onPress={submitFunction}
                style={{
                  minHeight: 32,
                  minWidth: 104,
                  paddingHorizontal: 12,
                  paddingVertical: 7,
                  borderRadius: 99,
                  borderWidth: 1,
                  borderColor: colors.stroke,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {loading ? (
                  <View style={styles.loaderContainer}>
                    {dotsArray.map((dot, index) => (
                      <View
                        key={dot}
                        style={[
                          styles.dot,
                          {
                            backgroundColor:
                              activeDot === index ? colors.black : colors.gray,
                          },
                        ]}
                      />
                    ))}
                  </View>
                ) : (
                  <P style={{ fontSize: 12 }}>Submit & exit</P>
                )}
              </TouchableOpacity>
            )}
          </View>
          <View
            style={{
              width: "90%",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              marginTop: 16,
            }}
          >
            {[1, 2, 3, 4].map((item, index) => (
              <View
                key={index}
                style={{
                  width: "23%",
                  height: 4,
                  borderRadius: 100,
                  backgroundColor:
                    index < currentStep ? colors.primary : colors.primarySubtle,
                }}
              />
            ))}
          </View>
        </View>
      ) : (
        <View
          // @ts-ignore
          style={[styles.navCont, contStyle, showBorder && styles.navBorder, {marginTop: expandSearch ? 16 : 24}]}
        >
          {/* @ts-ignore */}
          <View style={[styles.nav, navStyle]}>
            <TouchableOpacity
              onPress={handlePress}
              style={{ flexDirection: "row", alignItems: "center",}}
              disabled={disabled}
            >
              {showBackArrow && (
                <SvgXml xml={svg.goBackIcon} style={{ marginRight: expandSearch ? 0 : 8 }} />
              )}
              {!expandSearch && <P style={styles.navText}>{text}</P>}
            </TouchableOpacity>
            <P style={styles.navText}> </P>
            {iconComp}
            {expandSearch && (
              <View style={styles.searchContainer}>
                <View style={styles.searchInputContainer}>
                  <SvgXml width={18} height={18} xml={svg.search} style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search beneficiaries..."
                    placeholderTextColor={colors.gray}
                    value={searchValue}
                    onChangeText={onSearchChange}
                  />
                  {searchValue?.length > 0 && (
                    <TouchableOpacity
                      onPress={onClearPress}
                      style={styles.clearButton}
                    >
                      <SvgXml xml={svg.xClose} width={16} height={16} />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            )}

            {search && !expandSearch && (
              <TouchableOpacity
                onPress={() => {
                  setExpandSearch(true);
                }}
              >
                <SvgXml xml={svg.search} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  navCont: {
    width,
    height: 24,
    alignItems: "center",
    marginTop: 24,
    marginBottom: 16,
  },
  nav: {
    width: "90%",
    flexDirection: "row",
    height: "100%",
    alignItems: "center",
    paddingTop: 2,
  },
  navText: {
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  navBorder: {
    borderBottomWidth: 1,
    borderColor: "#313030",
  },
  loaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: 30, // Space for the 3 dots
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 100,
    backgroundColor: colors.gray, // Default inactive color
    marginHorizontal: 2,
  },
  searchContainer: {
    width: "90%",
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.white,
    borderRadius: 100,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: colors.stroke,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
});
