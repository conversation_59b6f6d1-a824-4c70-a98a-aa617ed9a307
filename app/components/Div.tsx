import React, { CSSProperties } from "react";
import { SafeAreaView, StyleSheet, StatusBar } from "react-native";
import Constants from "expo-constants";

interface PProps {
  children: any;
  style?: CSSProperties;
}

function Div({ children, style }: PProps) {
  return (
    <>
      {/* @ts-ignore */}
      <StatusBar style="light" />
      {/* @ts-ignore */}
      <SafeAreaView style={[styles.screen, style]}>{children}</SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  screen: {
    paddingTop: Constants.statusBarHeight,
    flex: 1,
    backgroundColor: "transparent",
    height: "100%",
  },
});
export default Div;
