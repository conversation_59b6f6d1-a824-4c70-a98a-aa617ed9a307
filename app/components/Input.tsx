import React, { CSSProperties, useState } from "react";
import {
  Dimensions,
  Pressable,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import P from "./P"; // Ensure P is correctly implemented or replace with a Text component
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";

interface PProps {
  label?: any;
  error?: boolean;
  value?: string;
  onChangeText?: (text: string) => void;
  onBlur?: (e) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  type?: "password";
  contStyle?: CSSProperties;
  inputStyle?: CSSProperties;
  leftIcon?: any;
  rightIcon?: any;
  editable?: boolean;
  keyboardType?: any;
  customInputStyle?: CSSProperties;
  labelStyle?: CSSProperties;
  numberOfLines?: number;
  onTogglePasswordVisibility?: () => void;
  onPress?: () => void;
  autoCapitalize?: string;
  maxLenght?: number;
  showPasswordStrength?: boolean;
}

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function Input({
  label,
  error,
  value,
  onBlur,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  type,
  contStyle,
  inputStyle,
  leftIcon,
  rightIcon,
  editable,
  keyboardType,
  customInputStyle,
  numberOfLines,
  onTogglePasswordVisibility,
  onPress,
  labelStyle,
  autoCapitalize,
  maxLenght,
  showPasswordStrength = false,
}: PProps) {
  const [focus, setFocus] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const togglePasswordVisibility = () => {
    if (onTogglePasswordVisibility) {
      // If the onTogglePasswordVisibility prop is provided, call it
      onTogglePasswordVisibility();
    } else {
      // Otherwise, toggle the password visibility as usual
      setIsPasswordVisible(!isPasswordVisible);
    }
  };

  const getPasswordStrength = (password: string) => {
    if (!password) return { level: "", color: colors.gray, dots: 0 };

    let score = 0;

    // Basic character type checks (1 point each)
    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumbers = /[0-9]/.test(password);
    const hasSpecialChars = /[*?!#$%&@^()\-_=+\\|[\]{};:/?.>]/.test(password);

    if (hasLowercase) score += 1;
    if (hasUppercase) score += 1;
    if (hasNumbers) score += 1;
    if (hasSpecialChars) score += 1;

    // Length scoring (more nuanced)
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;

    // Complexity bonuses
    const charTypeCount = [
      hasLowercase,
      hasUppercase,
      hasNumbers,
      hasSpecialChars,
    ].filter(Boolean).length;
    if (charTypeCount >= 3) score += 1;
    if (charTypeCount === 4) score += 1;

    // Pattern penalties (reduce score for common patterns)
    const commonPatterns = [
      /^[A-Z][a-z]+\d+[!@#$%^&*]?$/, // Password123! pattern
      /^[a-z]+\d+$/, // password123 pattern
      /^[A-Z]+\d+$/, // PASSWORD123 pattern
      /(.)\1{2,}/, // Repeated characters (aaa, 111)
      /123|abc|qwe|asd/i, // Sequential patterns
      /password|admin|user|login/i, // Common words
    ];

    let patternPenalty = 0;
    commonPatterns.forEach((pattern) => {
      if (pattern.test(password)) {
        patternPenalty += 1;
      }
    });

    // Apply pattern penalty
    score = Math.max(0, score - patternPenalty);

    // Determine strength level with more stringent requirements
    if (score <= 3) {
      return { level: "Weak", color: "#EF4444", dots: 1 };
    } else if (score <= 6) {
      return { level: "Medium", color: "#F59E0B", dots: 2 };
    } else {
      return { level: "Strong", color: "#10B981", dots: 3 };
    }
  };

  const passwordStrength =
    showPasswordStrength && type === "password"
      ? getPasswordStrength(value || "")
      : null;

  return (
    // @ts-ignore
    <View style={[styles.inputCont, contStyle]} onPress={onPress}>
      {label && (
        <View style={styles.labelContainer}>
          <P style={styles.label}>{label}</P>
          {passwordStrength && passwordStrength.level && (
            <View style={styles.strengthContainer}>
              <P style={styles.strengthLabel}>Security level</P>
              <View style={styles.strengthIndicator}>
                {[1, 2, 3].map((bar) => {
                  let barHeight = 2; // Default flat height
                  let barColor = "#E5E7EB"; // Default gray

                  if (passwordStrength.level === "Weak") {
                    barColor = "#EF4444"; // All bars red
                    if (bar === 1) barHeight = 4; // Only first bar has height
                  } else if (passwordStrength.level === "Medium") {
                    barColor = "#F59E0B"; // All bars yellow/orange
                    if (bar === 1) barHeight = 4; // First bar
                    if (bar === 2) barHeight = 6; // Second bar
                    // Third bar stays flat (2px)
                  } else if (passwordStrength.level === "Strong") {
                    barColor = "#027A48"; // All bars green
                    if (bar === 1) barHeight = 4; // First bar
                    if (bar === 2) barHeight = 6; // Second bar
                    if (bar === 3) barHeight = 8; // Third bar
                  }

                  return (
                    <View
                      key={bar}
                      style={[
                        styles.strengthBar,
                        {
                          height: barHeight,
                          backgroundColor: barColor,
                        },
                      ]}
                    />
                  );
                })}
                <P
                  style={[
                    styles.strengthText,
                    {
                      color:
                        passwordStrength.level === "Weak"
                          ? "#EF4444"
                          : passwordStrength.level === "Medium"
                          ? "#F59E0B"
                          : "#027A48",
                    },
                  ]}
                >
                  {passwordStrength.level}
                </P>
              </View>
            </View>
          )}
        </View>
      )}
      <View
        style={[
          styles.customInput,
          {
            borderColor: error
              ? colors.red
              : focus
              ? colors.primary
              : colors.stroke,
          },
          // @ts-ignore
          customInputStyle,
        ]}
      >
        {leftIcon}
        <TextInput
          value={value}
          numberOfLines={numberOfLines}
          onBlur={(e) => {
            setFocus(false);

            if (onBlur) onBlur(e);
          }}
          onChangeText={onChangeText}
          secureTextEntry={type === "password" && !isPasswordVisible}
          onFocus={() => setFocus(true)}
          placeholder={placeholder}
          placeholderTextColor={colors.gray}
          cursorColor={colors.black}
          editable={editable}
          keyboardType={keyboardType}
          autoCapitalize="none"
          maxLength={maxLenght}
          style={[
            styles.input,
            {
              width: type === "password" ? "85%" : "100%",
              pointerEvents: editable === false ? "none" : "auto",
            },
            // @ts-ignore
            inputStyle,
            {
              width:
                rightIcon && leftIcon
                  ? "75%"
                  : leftIcon || rightIcon
                  ? "88%"
                  : "95%",
            },
          ]}
        />
        {rightIcon}
        {type === "password" && (
          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={{
              right: 12,
              position: "absolute",
              width: 20,
              height: 20,
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <SvgXml
              xml={isPasswordVisible ? svg.eyeOpen : svg.eyeClose}
              style={{}}
              pointerEvents="none"
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  inputCont: {
    width: "100%",
  },
  labelContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 6,
  },
  label: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    lineHeight: 18,
    color: colors.black
  },
  strengthContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "center",
  },
  strengthLabel: {
    fontSize: 10,
    fontFamily: fonts.poppinsRegular,
    color: colors.gray,
  },
  strengthIndicator: {
    flexDirection: "row",
    alignItems: "flex-end",
    minHeight: 12,
    marginLeft: 4,
  },
  strengthBar: {
    width: 4,
    borderRadius: 2,
    marginRight: 2,
    marginBottom: 5,
  },
  strengthText: {
    fontSize: 10,
    fontFamily: fonts.poppinsMedium,
    marginRight: 6,
    alignSelf: "flex-end",
  },
  customInput: {
    width: "100%",
    borderWidth: 1,
    borderRadius: 8,
    height: (5.5 * height) / 100,
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    height: (44 / baseHeight) * height,
    paddingLeft: (14 / baseWidth) * width,
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    alignItems: "center",
    color: colors.black,
  },
});
