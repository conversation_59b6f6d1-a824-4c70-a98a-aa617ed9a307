import React, { useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  TextInput,
} from "react-native";
import P from "./P";
import { svg } from "../config/Svg";
import { colors } from "../config/colors";
import { fonts } from "../config/Fonts";
import ListItemSelect from "./ListItemSelect";
import { SvgXml } from "react-native-svg";
import { countries } from "./counties";

interface PProps {
  onPress?: any;
  onActiveCurrencyCode?: (index: String | null) => void;
  onActiveCountryChange?: (index: String | null) => void;
  onActiveYellowCard?: (index: String | null) => void;
  onActiveFlag?: (index: String | null) => void;
  onSymbolChange?: (index: String | null) => void;
  onActiveMobileCodeChange?: (index: String | null) => void;
  onActiveAlphaCodeChange?: (index: String | null) => void;
  onActiveAlphaCode2Change?: (index: String | null) => void;
  offHeader?: Boolean;
  excludedCountries?: any;
}

const { width, height } = Dimensions.get("window");

export default function CountrySelect({
  onPress,
  onActiveCurrencyCode,
  onActiveCountryChange,
  onActiveYellowCard,
  onActiveFlag,
  onSymbolChange,
  onActiveMobileCodeChange,
  onActiveAlphaCodeChange,
  onActiveAlphaCode2Change,
  offHeader = false,
  excludedCountries,
}: PProps) {
  const [activeType, setActiveType] = useState<number | null>(null);
  const [activeCountry, setActiveCountry] = useState<string>("");
  const [activeFlag, setActiveFlag] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [yellowCard, setYellowCard] = useState<string>("");
  const [currencyCode, setCurrencyCode] = useState<string>("");
  const [symbol, setSymbol] = useState<string>("");
  const [mobileCode, setMobileCode] = useState<string>("");
  const [alphaCode, setAlphaCode] = useState<string>("");
  const [alphaCode2, setAlphaCode2] = useState<string>("");

  // Array of countries to be excluded
  const filteredCountries = searchQuery
    ? countries
        .filter((country) =>
          country.country.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .filter(
          (country) => !excludedCountries?.includes(country?.country) // Exclude countries
        )
    : countries.filter(
        (country) => !excludedCountries?.includes(country?.country) // Exclude countries
      );

  useEffect(() => {
    if (onActiveCountryChange) {
      onActiveCountryChange(activeCountry);
    }
  }, [activeCountry]);
  useEffect(() => {
    if (onActiveAlphaCodeChange) {
      onActiveAlphaCodeChange(alphaCode);
    }
  }, [alphaCode]);

  useEffect(() => {
    if (onActiveAlphaCode2Change) {
      onActiveAlphaCode2Change(alphaCode2);
    }
  }, [alphaCode2]);

  useEffect(() => {
    if (onActiveMobileCodeChange) {
      onActiveMobileCodeChange(mobileCode);
    }
  }, [mobileCode]);

  useEffect(() => {
    if (onActiveYellowCard) {
      onActiveYellowCard(yellowCard);
    }
  }, [yellowCard]);
  useEffect(() => {
    if (onActiveCurrencyCode) {
      onActiveCurrencyCode(currencyCode);
    }
  }, [currencyCode]);
  useEffect(() => {
    if (onSymbolChange) {
      onSymbolChange(symbol);
    }
  }, [symbol]);

  useEffect(() => {
    if (onActiveFlag) {
      onActiveFlag(activeFlag);
    }
  }, [activeFlag]);

  return (
    <View style={styles.viewContent}>
      <View style={styles.search}>
        <SvgXml xml={svg.search} style={{ marginRight: 8 }} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search country"
          placeholderTextColor={colors.dGray}
          cursorColor={colors.black}
          value={searchQuery}
          onChangeText={(text) => setSearchQuery(text)}
        />
      </View>
      {!offHeader && (
        <P
          style={{
            fontSize: 12,
            color: colors.gray,
            fontFamily: fonts.poppinsRegular,
            marginBottom: 8,
          }}
        >
          Select the country you want to add money from
        </P>
      )}

      <ScrollView showsVerticalScrollIndicator={false}>
        {filteredCountries.map((item, index) => (
          <ListItemSelect
            text1={item.country}
            image={item.flag}
            key={index}
            isActive={activeType === index}
            onPress={() => {
              setActiveType(index);
              setActiveCountry(item.country);
              setActiveFlag(item.flag);
              setYellowCard(item.YellowCardCode);
              setCurrencyCode(item.currencyCode);
              setSymbol(item.symbol);
              setMobileCode(item.countryCode);
              setAlphaCode(item.alphaCode);
              setAlphaCode2(item.YellowCardCode);

              // Make sure onPress is called with the index
              if (typeof onPress === 'function') {
                onPress(index);
              }
            }}
            containerStyle={{
              marginBottom: index === filteredCountries.length - 1 ? 400 : 16,
            }}
          />
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  viewContent: {
    width: "100%",
    paddingTop: 24,
  },
  search: {
    width: "100%",
    height: (5.5 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    marginBottom: 24,
    borderWidth: 1,
    borderRadius: 99,
    borderColor: colors.stroke,
    padding: 12,
    paddingLeft: 14,
    paddingRight: 14,
    flexDirection: "row",
  },
  searchInput: {
    width: "90%",
    height: 24,
    fontFamily: fonts.poppinsRegular,
    lineHeight: 24,
    fontSize: 14,
  },
});
