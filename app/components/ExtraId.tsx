import React, { useEffect, useState } from "react";
import {
  Dimensions,
  Modal,
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  Image,
  ImageBackground,
} from "react-native";
import AuthenticationHedear from "./AuthenticationHedear";
import { colors } from "../config/colors";
import NoteComponent2 from "./NoteComponent2";
import Input from "./Input";
import { AddAdditionalId, CheckId } from "../RequestHandlers/User";
import Button from "./Button";
import { Formik } from "formik";
import * as yup from "yup";
import { fonts } from "../config/Fonts";
import { useToast } from "../context/ToastContext";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import H4 from "./H4";
import P from "./P";
import BottomSheet from "./BottomSheet";
import CountryCodeSelect from "./CountryCodeSelect";

const { width, height } = Dimensions.get("window");

interface PProps {
  close?: any;
  extraFunction?: any;
  type?: string;
}

export default function ExtraId({ close, extraFunction, type }: PProps) {
  const [showNin, setShowNin] = useState(true);
  const [showBvn, setShowBvn] = useState(true);
  const [showPhone, setShowPhone] = useState(true);
  const [loading, setLoading] = useState(false);
  const [showCountrySelect, setShowCountrySelect] = useState(false);
  const [countryCode, setCountryCode] = useState("+234");
  const [flag, setFlag] = useState({
    uri: "https://flagcdn.com/w2560/ng.png",
  });
  const { handleToast } = useToast();

  const checkId = async () => {
    try {
      const res = await CheckId();
      setShowBvn(!res.bvn); // Show BVN input if BVN is not set
      setShowNin(!res.nin); // Show NIN input if NIN is not set
      setShowPhone(!res.phoneNumber); // Show phone input if phone is not set
    } catch (error) {}
  };
  useEffect(() => {
    checkId();
  }, []);

  // Validation Schema
  const getValidationSchema = (
    showNin: boolean,
    showBvn: boolean,
    showPhone: boolean
  ) =>
    yup.object().shape({
      nin: showNin
        ? yup
            .string()
            .required("NIN is required")
            .matches(/^\d{11}$/, "NIN must be 11 digits")
        : yup.string().nullable(),
      bvn: showBvn
        ? yup
            .string()
            .required("BVN is required")
            .matches(/^\d{11}$/, "BVN must be 11 digits")
        : yup.string().nullable(),
      phoneNumber: showPhone
        ? yup
            .string()
            .required("Phone number is required")
            .min(4, "Invalid mobile number")
            .matches(
              /^[0-9]+$/,
              "Phone number should not include letters or white spaces"
            )
        : yup.string().nullable(),
    });

  const submit = async (value: any) => {
    const filteredValues = Object.fromEntries(
      Object.entries(value).filter(([, val]) => val !== "")
    );

    // If phone number is provided, combine with country code
    if (filteredValues.phoneNumber) {
      filteredValues.phoneNumber = `${countryCode}${filteredValues.phoneNumber
        .toString()
        .replace(/^0/, "")}`;
    }

    setLoading(true);
    try {
      const res = await AddAdditionalId(filteredValues);
      if (res.message === "Operation success") {
        extraFunction();
        handleToast(res.message);
      } else {
        handleToast(res.message);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal visible={true} style={{ flex: 1 }} statusBarTranslucent>
      <View style={{ width, height, backgroundColor: colors.white }}>
        <ImageBackground
          source={require("../assets/background.png")}
          style={styles.imageBackground}
          resizeMode="cover"
        />
        <View style={{ width: "95%", marginTop: 44, alignSelf: "center" }}>
          {/* <AuthenticationHedear
            text="Requirement"
            cancel={true}
            modalClose={close}
          /> */}
          <View
            style={{
              width: "90%",
              paddingVertical: 24,
              flexDirection: "row",
              justifyContent: "flex-end",
              alignSelf: "center",
            }}
          >
            <TouchableOpacity
              style={{
                paddingHorizontal: 12,
                paddingVertical: 7,
                borderRadius: 99,
                borderWidth: 1,
                borderColor: colors.stroke,
              }}
              onPress={close}
            >
              <P style={{ fontSize: 12 }}>Close</P>
            </TouchableOpacity>
          </View>

          <ScrollView automaticallyAdjustKeyboardInsets={true}>
            <Formik
              initialValues={{ nin: "", bvn: "", phoneNumber: "" }}
              validationSchema={getValidationSchema(
                showNin,
                showBvn,
                showPhone
              )}
              onSubmit={(values) => {
                submit(values);
              }}
            >
              {({
                handleChange,
                handleBlur,
                handleSubmit,
                values,
                errors,
                touched,
              }) => (
                <>
                  <View style={styles.detailWrap}>
                    <View
                      style={{
                        width: "100%",
                        alignItems: "center",
                        marginBottom: 24,
                      }}
                    >
                      <SvgXml
                        xml={type === "phone" ? svg.mobilePhone : svg.idBooklet}
                      />
                      <H4
                        style={{
                          fontFamily: fonts.poppinsSemibold,
                          marginTop: 24,
                        }}
                      >
                        {type === "phone"
                          ? "Phone number"
                          : "Verification requirement"}
                      </H4>
                      <P
                        style={{
                          marginTop: 4,
                          fontFamily: fonts.poppinsRegular,
                          textAlign: "center",
                        }}
                      >
                        {type === "phone"
                          ? "Provide your local phone number or that of a parent/guardian"
                          : "This helps us verify your identity and keep your account secure"}
                      </P>
                    </View>
                    <NoteComponent2
                      type="red"
                      contStyle={{ backgroundColor: colors.redSubtle }}
                      text={
                        "This one-time request helps streamline our process and ensures everything runs smoothly"
                      }
                    />
                    <>
                      {showPhone && (
                        <View>
                          <Input
                            label="Phone Number"
                            placeholder="**********"
                            contStyle={{ marginTop: 16 }}
                            onChangeText={(text) => {
                              // Remove all white spaces and non-numeric characters
                              const cleanedText = text
                                .replace(/\s/g, "")
                                .replace(/[^0-9]/g, "");
                              handleChange("phoneNumber")(cleanedText);
                            }}
                            value={values.phoneNumber}
                            keyboardType="numeric"
                            onBlur={handleBlur("phoneNumber")}
                            error={errors.phoneNumber && touched.phoneNumber}
                            leftIcon={
                              <TouchableOpacity
                                style={styles.countryCodeContainer}
                                onPress={() => {
                                  setShowCountrySelect(true);
                                }}
                              >
                                <Image
                                  source={flag}
                                  style={[
                                    styles.flagImage,
                                    {
                                      objectFit: flag?.uri?.includes("ng")
                                        ? "fill"
                                        : "cover",
                                    },
                                  ]}
                                />
                                <P style={styles.countryCodeText}>
                                  {countryCode}
                                </P>
                                <SvgXml xml={svg.dropDown} />
                              </TouchableOpacity>
                            }
                          />
                          {touched.phoneNumber && errors.phoneNumber && (
                            <Text style={styles.errorText}>
                              {errors.phoneNumber}
                            </Text>
                          )}
                        </View>
                      )}
                      {type === "phone" ? (
                        <></>
                      ) : (
                        <>
                          {showBvn && (
                            <View>
                              <Input
                                label="BVN"
                                contStyle={{ marginTop: 16 }}
                                placeholder="1122334455"
                                value={values.bvn}
                                keyboardType="numeric"
                                onChangeText={handleChange("bvn")}
                                onBlur={handleBlur("bvn")}
                                error={errors.bvn ? true : false}
                              />
                              {touched.bvn && errors.bvn && (
                                <Text style={styles.errorText}>
                                  {errors.bvn}
                                </Text>
                              )}
                            </View>
                          )}
                          {showNin && (
                            <View>
                              <Input
                                label="NIN"
                                contStyle={{ marginTop: 16 }}
                                placeholder="1122334455"
                                value={values.nin}
                                keyboardType="numeric"
                                onChangeText={handleChange("nin")}
                                onBlur={handleBlur("nin")}
                                error={errors.nin ? true : false}
                              />
                              {touched.nin && errors.nin && (
                                <Text style={styles.errorText}>
                                  {errors.nin}
                                </Text>
                              )}
                            </View>
                          )}
                        </>
                      )}
                    </>
                  </View>
                  <View
                    style={{
                      width: "80%",
                      alignSelf: "center",
                      marginTop: 32,
                    }}
                  >
                    <Button
                      btnText="Submit"
                      onPress={handleSubmit}
                      loading={loading}
                    />
                  </View>
                </>
              )}
            </Formik>
          </ScrollView>
        </View>

        {/* Country Code Selection BottomSheet */}
        <BottomSheet
          isVisible={showCountrySelect}
          backspaceText="Phone number"
          onClose={() => setShowCountrySelect(false)}
          showBackArrow={false}
          components={
            <CountryCodeSelect
              onPress={(country) => {
                setShowCountrySelect(false);
                setCountryCode(country.tel_code);
                setFlag({
                  uri: `https://flagcdn.com/w2560/${country.code.toLowerCase()}.png`,
                });
              }}
            />
          }
          modalContentStyle={{ height: "80%" }}
          extraModalStyle={{ height: "77%" }}
          componentHolderStyle={{ flex: 1 }}
        />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  countryCodeContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  flagImage: {
    width: 24,
    height: 24,
    marginRight: 8,
    borderRadius: 12,
  },
  countryCodeText: {
    fontSize: 14,
    fontFamily: fonts.poppinsRegular,
    color: colors.black,
    marginRight: 8,
  },
  imageBackground: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 120,
  },
});
