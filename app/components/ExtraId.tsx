import React, { useEffect, useState } from "react";
import {
  Dimensions,
  Modal,
  View,
  StyleSheet,
  ScrollView,
  Text,
} from "react-native";
import AuthenticationHedear from "./AuthenticationHedear";
import { colors } from "../config/colors";
import NoteComponent2 from "./NoteComponent2";
import Input from "./Input";
import { AddAdditionalId, CheckId } from "../RequestHandlers/User";
import Button from "./Button";
import { Formik } from "formik";
import * as yup from "yup";
import { fonts } from "../config/Fonts";
import { useToast } from "../context/ToastContext";

const { width, height } = Dimensions.get("window");

interface PProps {
  close?: any;
  extraFunction?: any;
}

export default function ExtraId({ close, extraFunction }: PProps) {
  const [showNin, setShowNin] = useState(true);
  const [showBvn, setShowBvn] = useState(true);
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();

  const checkId = async () => {
    try {
      const res = await CheckId();
      setShowBvn(!res.bvn); // Show BVN input if BVN is not set
      setShowNin(!res.nin); // Show NIN input if NIN is not set
    } catch (error) {}
  };
  useEffect(() => {
    checkId();
  }, []);

  // Validation Schema
  const getValidationSchema = (showNin: boolean, showBvn: boolean) =>
    yup.object().shape({
      nin: showNin
        ? yup
            .string()
            .required("NIN is required")
            .matches(/^\d{11}$/, "NIN must be 11 digits")
        : yup.string().nullable(),
      bvn: showBvn
        ? yup
            .string()
            .required("BVN is required")
            .matches(/^\d{11}$/, "BVN must be 11 digits")
        : yup.string().nullable(),
    });

  const submit = async (value) => {
    const filteredValues = Object.fromEntries(
      Object.entries(value).filter(([key, val]) => val !== "")
    );
    setLoading(true);
    try {
      const res = await AddAdditionalId(filteredValues);
      if (res.message === "Operation success") {
        extraFunction();
        handleToast(res.message);
      } else {
        handleToast(res.message);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal visible={true} style={{ flex: 1 }} statusBarTranslucent>
      <View style={{ width, height, backgroundColor: colors.secBackground }}>
        <View style={{ width: "95%", marginTop: 44, alignSelf: "center" }}>
          <AuthenticationHedear
            text="Requirement"
            cancel={true}
            modalClose={close}
          />
          <ScrollView automaticallyAdjustKeyboardInsets={true}>
            <Formik
              initialValues={{ nin: "", bvn: "" }}
              validationSchema={getValidationSchema(showNin, showBvn)}
              onSubmit={(values) => {
                submit(values);
              }}
            >
              {({
                handleChange,
                handleBlur,
                handleSubmit,
                values,
                errors,
                touched,
              }) => (
                <>
                  <View style={styles.detailWrap}>
                    <NoteComponent2
                      text={
                        "Please note that the requirement to use the preferred method is a one-time request that simplifies our process and ensures everything runs smoothly"
                      }
                    />
                    <>
                      {showNin && (
                        <View>
                          <Input
                            label="NIN"
                            contStyle={{ marginTop: 16 }}
                            placeholder="1122334455"
                            value={values.nin}
                            keyboardType="numeric"
                            onChangeText={handleChange("nin")}
                            onBlur={handleBlur("nin")}
                            error={errors.nin ? true : false}
                          />
                          {touched.nin && errors.nin && (
                            <Text style={styles.errorText}>{errors.nin}</Text>
                          )}
                        </View>
                      )}

                      {showBvn && (
                        <View>
                          <Input
                            label="BVN"
                            contStyle={{ marginTop: 16 }}
                            placeholder="1122334455"
                            value={values.bvn}
                            keyboardType="numeric"
                            onChangeText={handleChange("bvn")}
                            onBlur={handleBlur("bvn")}
                            error={errors.bvn ? true : false}
                          />
                          {touched.bvn && errors.bvn && (
                            <Text style={styles.errorText}>{errors.bvn}</Text>
                          )}
                        </View>
                      )}
                    </>
                  </View>
                  <View
                    style={{
                      width: "75%",
                      alignSelf: "center",
                      marginTop: 32,
                    }}
                  >
                    <Button
                      btnText="Next"
                      onPress={handleSubmit}
                      loading={loading}
                    />
                  </View>
                </>
              )}
            </Formik>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  detailWrap: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 24,
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
