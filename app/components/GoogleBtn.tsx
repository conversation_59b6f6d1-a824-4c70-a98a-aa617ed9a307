import React, { useState, CSSProperties, useEffect, useContext } from "react";
import { View, TouchableOpacity, StyleSheet, Dimensions } from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import { colors } from "../config/colors";
import i18n from "../../i18n";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { ValidateGoogleToken } from "../RequestHandlers/Authentication";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import { useToast } from "../context/ToastContext";
import { withApiErrorToast } from "../Utils/withApiErrorToast";
interface PProps {
  contStyle?: CSSProperties;
  onPress?: any;
  navigation?: any;
}
const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function GoogleBtn({ contStyle, onPress, navigation }: PProps) {
  const { handleToast } = useToast();
  const [activeDot, setActiveDot] = useState(0);
  const [loading, setLoading] = useState(false);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const dotsArray = [1, 2, 3];
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length);
      }, 200);
      return () => clearInterval(interval);
    }
  }, [loading]);
  const func = () => {
    console.log("Attempting navigation with delay in func()");
    setTimeout(() => {
      try {
        navigation.reset({
          index: 0,
          routes: [
            {
              name: "BottomTabNavigator",
            },
          ],
        });
      } catch (error) {
        console.error("Navigation error in func():", error);
        // Fallback navigation if the first attempt fails
        try {
          navigation.navigate("BottomTabNavigator");
        } catch (innerError) {
          console.error("Fallback navigation error in func():", innerError);
        }
      }
    }, 500);
  };
  const persistLogin = (credentail: any, idToken: string) => {
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(() => {
        console.log("Credentials saved to AsyncStorage");
        // @ts-ignore
        setStoredCredentails(credentail);
      })
      .catch((err) => {
        console.error("Error in login process:", err);
      });
  };
  const persistLogin2 = (credentail: any, idToken: string) => {
    AsyncStorage.setItem("cookies", JSON.stringify(credentail))
      .then(() => {
        console.log("Credentials saved to AsyncStorage");
        // @ts-ignore
        setStoredCredentails(credentail);
        setTimeout(() => {
          navigation.reset({
            index: 0,
            routes: [
              {
                name: "BottomTabNavigator",
              },
            ],
          });
        }, 500);
      })
      .catch((err) => {
        console.error("Error in login process:", err);
      });
  };
  const validateGoogleToken = async (idToken) => {
    try {
      const validateToken = await withApiErrorToast(ValidateGoogleToken({
        idToken: idToken,
      }), handleToast);
      AsyncStorage.setItem("GToken", idToken)
        .then(() => console.log("Google token saved successfully!"))
        .catch((error) => console.error("Error saving Google token:", error));
      if (validateToken.newAccount === true) {
        await AsyncStorage.removeItem("hasLoggedInBefore");
        await AsyncStorage.setItem("newUser", "true");
      } else {
        await AsyncStorage.setItem("hasLoggedInBefore", "true");
      }
      if (validateToken.token) {
        if (validateToken.user._2faEnabled === true) {
          navigation.reset({
            index: 0,
            routes: [
              {
                name: "TwofactorAuthScreen2",
                params: {
                  type: "login",
                  tkn: validateToken.token,
                  ActivityFunction: () => persistLogin2(validateToken, idToken),
                },
              },
            ],
          });
        } else {
          setTimeout(() => {
            persistLogin(validateToken, idToken);
          }, 500);
        }
      } else {
        handleToast(validateToken.message, "error");
        await GoogleSignin.signOut();
      }
    } catch (error) {
      handleToast(error.message, "error");
    } finally {
      setLoading(false);
    }
  };
  const SignIn = async () => {
    const hasPreviousSignIn = GoogleSignin.hasPreviousSignIn();
    if (hasPreviousSignIn) {
      await GoogleSignin.signOut();
    } else {
      let _user = await GoogleSignin.getCurrentUser();
      if (!_user) {
        const user = await GoogleSignin.signIn();
        if (user.data.idToken) {
          setLoading(true);
          validateGoogleToken(user.data.idToken);
        } else {
        }
      }
      if (!_user.idToken) throw new Error("Auth Failed. Contact SFX!");
    }
  };
  return (
    // @ts-ignore
    <View style={[{ alignItems: "center" }, contStyle]}>
      <TouchableOpacity
        style={{
          flexDirection: "row",
          alignItems: "center",
          width: "100%",
          justifyContent: "center",
          borderWidth: 1,
          borderColor: colors.stroke,
          borderRadius: 99,
          height: (44 / baseHeight) * height,
        }}
        onPress={SignIn}
      >
        {loading ? (
          <View style={styles.loaderContainer}>
            {dotsArray.map((dot, index) => (
              <View
                key={dot} // Use the dot value as key
                style={[
                  styles.dot,
                  {
                    backgroundColor:
                      activeDot === index ? colors.black : colors.gray,
                  },
                ]}
              />
            ))}
          </View>
        ) : (
          <>
            <SvgXml xml={svg.google} style={{ marginRight: 4 }} />
            <P style={{ color: colors.black, fontSize: 12 }}>
              {i18n.t("google")}
            </P>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: 30, // Space for the 3 dots
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 100,
    backgroundColor: colors.gray, // Default inactive color
    marginHorizontal: 2,
  },
});
