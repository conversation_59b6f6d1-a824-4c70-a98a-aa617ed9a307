import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";

const request = new RequestHandler();

export function GetUserWallet(country?: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        const url = country
          ? `wallet/get-user-wallets?country=${country}`
          : `wallet/get-user-wallets`;
        request.get(url, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetWalletById(walletId: any): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`wallet/get-user-wallets/${walletId}`, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}

export function GetAllRate(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;
        request.get(`wallet/yellow-card/rates/get`, "", [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetAllSFxRate(from?: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;

        request.get(
          `wallet/rate/get?type=all&from=${from ? from : "USD"}`,
          "",
          [
            (data, error) => {
              if (error) {
                reject(error);
              } else {
                resolve(data);
              }
            },
          ]
        );
      })
      .catch((error) => reject(error));
  });
}

export function GetRateById(currencyCode: String): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;
        request.get(
          `wallet/yellow-card/rates/get?currency=${currencyCode}`,
          "",
          [
            (data, error) => {
              if (error) {
                reject(error);
              } else {
                resolve(data);
              }
            },
          ]
        );
      })
      .catch((error) => reject(error));
  });
}

export function GetRateByCountry(
  currencyCode: String,
  provider: String
): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;
        request.get(
          `wallet/rate/get?type=all&currency=${currencyCode}&provider=${provider}`,
          "",
          [
            (data, error) => {
              if (error) {
                reject(error);
              } else {
                resolve(data);
              }
            },
          ]
        );
      })
      .catch((error) => reject(error));
  });
}
export function GetNetworkFee(chain: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;
        request.get(`wallet/rate/get-fee?chain=${chain}`, "", [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetChannels(country): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;
        request.get(`wallet/yellow-card/channels/get?country=${country}`, "", [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetNetwork(country): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;
        request.get(`wallet/yellow-card/network/get?country=${country}`, "", [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetManualNetwork(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;
        request.get(`wallet/yellow-card/network/get`, "", [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetMinMAx(country, gateway, type): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        // let token = infomation.token;
        request.get(
          `wallet/yellow-card/min-max?country=${country}&gateway=${gateway}&type=${type}`,
          "",
          [
            (data, error) => {
              if (error) {
                reject(error);
              } else {
                resolve(data);
              }
            },
          ]
        );
      })
      .catch((error) => reject(error));
  });
}
export function DepositBank(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`wallet/yellow-card/deposit`, body, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}

export function GetTransationById(transactionId: string): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(
          `wallet/transaction/v2/get/{transactionId}?transactionId=${transactionId}`,
          token,
          [
            (data, error) => {
              if (error) {
                reject(error);
              } else {
                resolve(data);
              }
            },
          ]
        );
      })
      .catch((error) => reject(error));
  });
}
export function GetTransation(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(
          `wallet/transaction/v2/get?page=${"1"}&limit=${"10"}&type=${"all"}&status=${"all"}`,
          token,
          [
            (data, error) => {
              if (error) {
                reject(error);
              } else {
                resolve(data);
              }
            },
          ]
        );
      })
      .catch((error) => reject(error));
  });
}
export function GetCurrencyTransactions(currency: any): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(
          `wallet/transaction/v2/get?page=1&limit=20&currency=${currency}`,
          token,
          [
            (data, error) => {
              if (error) {
                reject(error);
              } else {
                resolve(data);
              }
            },
          ]
        );
      })
      .catch((error) => reject(error));
  });
}
export function GetCardTransactions(
  page?: number,
  limit?: number,
  id?: string
): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        const uri = `wallet/transaction/v2/get?page=${page}&limit=${limit}&type=card&cardId=${id}`;
        request.get(uri, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetTransationByFilters(
  page?: number,
  limit?: number,
  type?: string,
  status?: string,
  id?: string,
  date?: any
): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        const uri = date
          ? `wallet/transaction/v2/get?page=${page}&limit=${limit}&type=${type}&status=${status}&cardId=${id}&date=${date}`
          : `wallet/transaction/v2/get?page=${page}&limit=${limit}&type=${type}&status=${status}&cardId=${id}`;
        request.get(uri, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetNewTransationByFilters(
  page?: number,
  limit?: number,
  type?: string,
  status?: string,
  gateWay?: string,
  id?: string,
  date?: any
): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        const uri = date
          ? `wallet/transaction/v2/new/get?page=${page}&limit=${limit}&type=${type}&status=${status}&gateway=${gateWay}&cardId=${id}&date=${date}`
          : `wallet/transaction/v2/new/get?page=${page}&limit=${limit}&type=${type}&status=${status}&gateway=${gateWay}&cardId=${id}`;
        request.get(uri, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function SendSFXMoneyApp(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`wallet/transaction/transfer/internal`, body, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function MomoSendMoney(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`wallet/yellow-card/withdraw`, body, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}

export function ExternalTransfer(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`wallet/transaction/transfer/external`, body, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}

export function GetAccountName(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`wallet/yellow-card/resolve-account-name`, body, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}

export function GetLinkVendors(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`wallet/link/vendors/get?currency=NGN`, "", [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetLinkBanks(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`wallet/link/payment-bank/get?currency=NGN`, "", [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetLinkWallet(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`wallet/link/payment-wallet/get`, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetLinkDepositRate(): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(`wallet/link/deposit/rates/get`, "", [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function GetYellowCardEstimatedFee(
  country: string,
  amount: number,
  gateway: string,
  type: string
): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.get(
          `wallet/yellow-card/estimate-fee?country=${country}&amount=${amount}&gateway=${gateway}&type=${type}`,
          token,
          [
            (data, error) => {
              if (error) {
                reject(error);
              } else {
                resolve(data);
              }
            },
          ]
        );
      })
      .catch((error) => reject(error));
  });
}

export function DepositeLink(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`wallet/link/deposit`, body, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
export function LinkGetAccountName(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`wallet/link/resolve-account-name`, body, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}

export function LinkWithdraw(body: object): Promise<any> {
  return new Promise((resolve, reject) => {
    AsyncStorage.getItem("cookies")
      .then((res) => {
        let infomation = JSON.parse(res);
        let token = infomation.token;
        request.post(`wallet/link/withdraw`, body, token, [
          (data, error) => {
            if (error) {
              reject(error);
            } else {
              resolve(data);
            }
          },
        ]);
      })
      .catch((error) => reject(error));
  });
}
