import * as React from "react";
import { useState, useEffect, useContext } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import BottomTabNavigator from "./BottomTabNavigator";
import AsyncStorage from "@react-native-async-storage/async-storage";
// import ReferralScreen from "../screens/ReferralScreen";
import BillPaymentScreen from "../screens/BillPaymentScreen";
import OnboardingScreen from "../screens/OnboardingScreen";
import NewLoginScreen from "../screens/NewLoginScreen";
import ForgotPasswordScreen from "../screens/ForgotPasswordScreen";
import VerifyEmailScreen from "../screens/VerifyEmailScreen";
import ResetPasswordScreen from "../screens/ResetPasswordScreen";
import NotificationScreen from "../screens/NotificationScreen";
import ResetSuccessScreen from "../screens/ResetSuccessScreen";
import ConfirmDetailScreen from "../screens/AddMoneyBankAccount/ConfirmDetailsScreen";
import BankTransferScreen from "../screens/AddMoneyBankAccount/BanKTransferScreen";
import SignupScreen from "../screens/SignupScreen";
import VerifyEmailScreen2 from "../screens/VerifyEmailScreen2";
import ConfirmInformationScreen from "../screens/ConfirmInformationScreen";
import VerifyIdentity from "../screens/VerifyIdentity";
import CreatenameScreen from "../screens/CreatenameScreen";
import TransactionPinScreen from "../screens/TransactionPinScreen";
import SignupSuccessScreen from "../screens/SignupSuccessScreen";
import MobileMoneyScreen from "../screens/MobileMoneyScreen";
import FaceIdScreen from "../screens/FaceIdScreen";
import SendMoneyScreen from "../screens/SendMoneyScreen";
import MobileMoneyScreen1 from "../screens/sendMoney/MobileMoneyScreen1";
import MobileMoneyScreen2 from "../screens/sendMoney/MobileMoneyScreen2";
import AmountScreen1 from "../screens/sendMoney/AmountScreen1";
import ConfirmDetailScreen1 from "../screens/sendMoney/ConfirmDetailsScreen1";
import TransactionDetails1 from "../screens/sendMoney/TransactionDetails1";
import P2pScreen from "../screens/P2p/P2pScreen";
import TransactionReceipt from "../screens/sendMoney/TransactionReciept";
import SelectAccountScreen from "../screens/sfx-money-screens/SelectAccount.screen";
import AccountDetailsEntry from "../screens/sfx-money-screens/AccountDetailsEntry.screen";
import AmountScreen from "../screens/sfx-money-screens/AmountScreen";
import SfxConfirmDetailsScreen from "../screens/sfx-money-screens/SfxConfirmDetailsScreen";
import TransactionScreen from "../screens/sfx-money-screens/TransactionScreen.screen";
import TransactionStatus from "../screens/sfx-money-screens/TransactionStatus.screen";
import SfxTransactionDetails from "../screens/sfx-money-screens/SfxTransactionDetails";
import SfxTransactionReciept from "../screens/sfx-money-screens/SfxTransactionReciept";
import BankAmountScreen1 from "../screens/bankAccount/BankAmountScreen1";
import BankConfirmDetailScreen1 from "../screens/bankAccount/BankConfirmDetailsScreen1";
import BankTransactionDetails1 from "../screens/bankAccount/BankTransactionDetails1";
import BankTransactionReceipt from "../screens/bankAccount/BankTransactionReciept";
import BankMobileMoneyScreen1 from "../screens/bankAccount/BankMobileMoneyScreen1";
import BankMobileMoneyScreen2 from "../screens/bankAccount/BankMobileMoneyScreen2";
import P2pScreen2 from "../screens/P2p/P2pScreen2";
import P2pAmountScreen from "../screens/P2p/P2pAmountScreen";
import P2pConfirmDetails from "../screens/P2p/P2pConfirmDetails";
import P2pTransactionPin from "../screens/P2p/P2pTransactionPin";
import P2pTransactionDetails from "../screens/P2p/P2pTransactionDetails";
import P2pTransactionReceipt from "../screens/P2p/P2pTransactionReciept";
import AddMoneyScreen from "../screens/AddMoneyScreen";
import AddMoneyAccountSelectScreen from "../screens/AddMoneySfxMoneyApp/AddMoneyAccountSelect";
import AddMoneyAccountScreen from "../screens/AddMoneySfxMoneyApp/AddMoneyAccountScreen";
import AddMoneyTransactionDetailsSfxMoneyApp from "../screens/AddMoneySfxMoneyApp/AddMoneyTransactionDetailsSfxMoneyApp";
import AddMoneyBankAccountScreen from "../screens/AddMoneyBankAccount/AddMoneyBankAccountScreen";
import AddMoneyAmountScreen1 from "../screens/AddMoneyBankAccount/AddMoneyAmountInput";
import BankTransactionDetails2 from "../screens/AddMoneyBankAccount/BankTransactionDetails2";
import MoneySentScreen1 from "../screens/AddMoneyBankAccount/MoneySentScreen1";
import AddMoneyMobileMoneyAccountSelect from "../screens/AddMoneyMobileMoney/AddMoneyMobileMoneyAccountSelect";
import AddMoneyAmountScreen2 from "../screens/AddMoneyMobileMoney/AddMoneyAmountInputScreen2";
import ConfirmDetailScreen2 from "../screens/AddMoneyMobileMoney/ConfirmDetailsScreen2";
import MobileMoneyTransfar from "../screens/AddMoneyMobileMoney/MobileMoneyTransfar";
import MobileMoneyTransactionDetails from "../screens/AddMoneyMobileMoney/MobileMoneyTransactionDetails";
import MoneySentScreen2 from "../screens/AddMoneyMobileMoney/MoneySentScreen2";
import AddAmountP2pScreen from "../screens/AddMoneyP2p/AddMoneyP2pScreen";
import AddMoneyP2pScreen from "../screens/AddMoneyP2p/AddMoneyP2pScreen";
import AddMoneyP2pAccountScreen from "../screens/AddMoneyP2p/AddMoneyP2pAccountScreen";
import AddMoneyP2pTransactionDetails from "../screens/AddMoneyP2p/AddMoneyP2pTransactionDetails";
import MobileMoneyTransactionPin from "../screens/sendMoney/MobileMoneyTransactionPin";
import BankTransactionPin from "../screens/bankAccount/BankTransactionPin";
import BillPaymentIndex from "../screens/BillPaymentIndex";
import WaterIndex from "../screens/Water/WaterIndex";
import WaterAmountScreen from "../screens/Water/WaterAmountScreen";
import WaterTransactionDetails from "../screens/Water/WaterTransactionDetails";
import ElecricityIndex from "../screens/Electricity/ElecricityIndex";
import ElectricityAmountScreen from "../screens/Electricity/ElectricityAmountScreen";
import ElectricityTransactionDetails from "../screens/Electricity/ElectricityTransactionDetails";
import AirtimeTransactionDetails from "../screens/Airtime/AirtimeTransactionDetails";
import AirtimeAmountScreen from "../screens/Airtime/AirtimeAmountScreen";
import AirtimeIndex from "../screens/Airtime/AirtimeIndex";
import InternetIndex from "../screens/Internet/InternetIndex";
import InternetAmountScreen from "../screens/Internet/InternetAmountScreen";
import InternetTransactionDetails from "../screens/Internet/InternetTransactionDetails";
import BankNoticeScreen from "../screens/BankNoticeScreen";
import BankNoticeScreen1 from "../screens/BankNoticeScreen1";
import CardGetStartedScreen from "../screens/Card/CardGetStartedScreen";
import CardApplicationScreen from "../screens/Card/CardApplicationScreen";
import PhysicalCardPromt from "../screens/Card/PhysicalCardPromt";
import AccountVerificationPromt from "../screens/Card/AccountVerificationPrompt";
import AccountVerification1 from "../screens/AccountVerification/AccountVerification1";
import AccountVerification2 from "../screens/AccountVerification/AccountVerification2";
import AccountVerification3 from "../screens/AccountVerification/AccountVerification3";
import AccountVerification4 from "../screens/AccountVerification/AccountVerification4";
import FaceIdScreen2 from "../screens/AccountVerification/FaceIDScreen2";
import AllSetScreen from "../screens/AccountVerification/AllSetScreen";
import PersonalInfo from "../screens/Card/PersonalInfo";
import CardTypeScreen from "../screens/Card/CardTypeScreen";
import CardFeeDetailsScreen from "../screens/Card/CardFeeDetailsScreen";
import CardScreen from "../screens/Card/CardScreen";
import PinManagementScreen from "../screens/Card/PinManagementScreen";
import CreateCardPin from "../screens/Card/CreateCardPin";
import ConfirmCardPin from "../screens/Card/ConfirmCardPin";
import CardSendScreen from "../screens/Card/CardSendScreen";
import CardSendDetails from "../screens/Card/CardSendDetails";
import CardPin from "../screens/Card/CardPin";
import CardSendStatus from "../screens/Card/CardSendStatus";
import CardTopUp from "../screens/Card/CardTopUp";
import CardTopUpDetails from "../screens/Card/CardTopUpDetails";
import TransactionPinScreen2 from "../screens/Card/TransactionPinScreen2";
import CardLimitScreen from "../screens/Card/CardLimitScreen";
import CardManagementScreen from "../screens/Card/CardManagementScreen";
import FreezeCardPin from "../screens/Card/FreezeCardPin";
import ChangePin1 from "../screens/Card/ChangePin1";
import ChangePin2 from "../screens/Card/ChangePin2";
import ChangePin3 from "../screens/Card/ChangePin3";
import DeleteCardPromt from "../screens/Card/DeleteCardPromt";
import DeleteCardSelect from "../screens/Card/DeleteCardSelect";
import ProfileIndex from "../screens/Profile/ProfileIndex";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import SwapScreen from "../screens/Swap/SwapScreen";
import SwapScreenPromt from "../screens/Swap/SwapScreenPromt";
import ProfileAccountTier from "../screens/Profile/ProfileAccountTier";
import ProfileUpgradeAccount from "../screens/Profile/ProfileUpgradeAccount";
import SecurityIndex from "../screens/Security/SecurtityIndex";
import SecurtityChangePassword from "../screens/Security/SecurtityChangePassword";
import SecurtityEmailVerification from "../screens/Security/SecurtityEmailVerification";
import SecurtityChangeTransactionPin from "../screens/Security/SecurtityChangeTransactionPin";
import AddMoneyRate from "../screens/Rate/AddMoneyRate";
import WalletViewScreen from "../screens/wallet/WalletViewScreen";
import WalletDetails from "../screens/wallet/WalletDetails";
import RewardScreen from "../screens/wallet/RewardScree";
import PointDetailsScreen from "../screens/wallet/PointDetailsScreen";
import PointTransactionDetails from "../screens/wallet/PointTransactionDetails";
import Rules from "../screens/wallet/Rules";
import SecurtityQuestion from "../screens/Security/SecurtityQuestion";
import FaceId from "../screens/Security/FaceId";
import NotificationIndex from "../screens/notification/NotificationIndex";
import ReferralScreen from "../screens/ReferalSettings/ReferralScreen";
import RulesScreen from "../screens/ReferalSettings/RulesScreen";
import AllTransactionsDetails from "../screens/TransactionDetails/AllTransactionDetails";
import AllSetScreen2 from "../screens/AllSetScreen2";
import ConfirmNewPin from "../screens/Security/ConfirmNewPin";
import ReferralListScreen from "../screens/ReferalSettings/ReferralListScreen";
import AboutUs from "../screens/AboutUs";
import AllRecieptScreen from "../screens/TransactionDetails/AllRecieptScreen";
import MainRewardScreen from "../screens/wallet/MainRewardScreen";
import ExistingLoginScreen from "../screens/ExistingLoginScreen";
import Rate2 from "../screens/Rate/Rate2";
import AddMoneyRate2 from "../screens/Rate/AddMoneyRate2";
import Discover2 from "../screens/Discover2";
import CardColor from "../screens/Card/CardColor";
import OnlineMarchent from "../screens/Card/OnlineMarchent";
import Beneficiaries from "../screens/Beneficiaries";
import ExistingFp from "../screens/ExistingFp";
import ExistingRp from "../screens/ExistingRp";
import ResetSuccessScreen2 from "../screens/ResetSuccessScreen2";
import ConfirmTransactionPin from "../screens/AccountVerification/ConfirmTransactionPin";
import AccountVerificationPromt1 from "../screens/Card/AccountVerificationPromt1";
import SendMoneyStatus from "../components/SeendMoneyStatus";
import AllRTransactionsDetails from "../screens/TransactionDetails/AllRTransactionDetails";
import AllRRecieptScreen from "../screens/TransactionDetails/AllRRecieptScreen";
import CardPayPinScreen from "../screens/Card/CardPayPinScreen";
import CardPayStatusScreen from "../screens/Card/CardPayStatusScreen";
import CardTopStatus from "../screens/Card/CardTopStatus";
import CardTransactionDetails from "../screens/Card/CardTransactionDetailes";
import CardReciept from "../screens/Card/CardReciept";
import DeleteCardPin from "../screens/Card/DeleteCardPin";
import CardFeeTransactionDetails from "../screens/Card/CardFeeTransactionDetails";
import CardFeeReciept from "../screens/Card/CardFeeReciept";
import CardHistory from "../screens/Card/CardHistory";
import EditSfxBene from "../screens/EditBene/EditSfxBene";
import LinkAddAmount from "../screens/Link/LinkAdd/LinkAddAmount";
import LinkAddAccountSelect from "../screens/Link/LinkAdd/LinkAddAccountSelect";
import LinkAddConfirmDetail from "../screens/Link/LinkAdd/LinkAddConfirmDetail";
import LinkTransferScreen from "../screens/Link/LinkAdd/LinkTransferScreen";
import LinkSentMoney from "../screens/Link/LinkAdd/LinkSentMoney";
import LinkTransactionDetails from "../screens/Link/LinkAdd/LinkTransactionDetails";
import LinkReciept from "../screens/Link/LinkAdd/LinkReciept";
import LinkSendAmount from "../screens/Link/LinkSend/LinkSendAmount";
import LinkSendConfirmDetails from "../screens/Link/LinkSend/LinkSendConfirmDetail";
import LinkTransactionPin from "../screens/Link/LinkSend/LinkTransactionPin";
import LinkSendStatus from "../screens/Link/LinkSend/LinkSendStatus";
import LinkSendTransactionDetails from "../screens/Link/LinkSend/LinkSendTransactionDetails";
import LinkSendReciept from "../screens/Link/LinkSend/LinkSendReciept";
import AccountVerificationPending from "../screens/AccountVerification/AccountVerificationPending";
import { navigationRef, navigate } from "./navigationRef";
import { CardAddNameScreen } from "../screens/Card/CardAddNameScreen";
import BetaTest from "../screens/BetaTest";
import BillingAddressScreen from "../screens/Card/BillingAddressScreen";
import ReferralProgramScreen from "../screens/ReferalProgram/ReferalProgramScreen";
import ReferralProgramListScreen from "../screens/ReferalProgram/ReferalProgramListScreen";
import NewReferralRules from "../screens/ReferalProgram/NewReferralRules";
import AllGuideScreen from "../screens/Guide/AllGuideScreen";
import VerifySession from "../screens/VerifySessionScreen";
import VerifyActivityScreen from "../screens/Security/VerifyActivityScreen";
import TwofacttoAuthScreen from "../screens/Security/2fa/TwofactorAuthScreen1";
import TwofacttoAuthScreen2 from "../screens/Security/2fa/TwofactorAuthScreen2";
import TwofactorBackupCode from "../screens/Security/2fa/TwoFactorBackupCodeScreen";
import TwofactorBackupCodeScreen from "../screens/Security/2fa/TwoFactorBackupCodeScreen";
import OTCDeckSelectScreen from "../screens/OTCdeck/OTCDeckSelectScreen";
import OTCTransactionScreen from "../screens/OTCdeck/OTCTransactionScreen";
import InAppBrowserScreen from "../screens/InAppBrowserScreen";
import ReferralContestScreen from "../screens/ReferalProgram/ReferralContestScreen";
import ContestRulesScreen from "../screens/ReferalProgram/ContenstRulesScreen";
import ViewMoreLeaderboard from "../screens/ReferalProgram/ViewMoreLeaderboard";
import OTCAccountDetailScreen from "../screens/OTCdeck/OTCSend/OTCAccountDetailsScreen";
import OTCConfirmTransactionScreen from "../screens/OTCdeck/OTCSend/OTCConfirmTransactionScreen";
import OTCPinScreen from "../screens/OTCdeck/OTCSend/OTCPinScreen";
import OTCTransactionStatus from "../screens/OTCdeck/OTCSend/OTCTransactionStatus";
import MobileNoteScreen from "../screens/AddMoneyMobileMoney/MobileNoteScreen";
import SumSubKYCScreen from "../screens/AccountVerification/SumSubKYCScreen";

const Stack = createStackNavigator();

// Create a wrapper component to handle the credentials context
function MainStackWithCredentials() {
  const { storedCredentails } = useContext(CredentailsContext);
  const [justLoggedIn, setJustLoggedIn] = useState(false);
  const [isCheckingLoginStatus, setIsCheckingLoginStatus] = useState(true);

  // Function to check login status
  const checkLoginStatus = async () => {
    setIsCheckingLoginStatus(true);
    try {
      const hasJustLoggedIn = await AsyncStorage.getItem("justLoggedIn");
      if (hasJustLoggedIn === "true") {
        console.log("User just logged in, showing BottomTabNavigator first");
        setJustLoggedIn(true);
        // We'll remove this flag in the HomeScreen component
      } else {
        console.log("Not a fresh login, showing normal flow");
        setJustLoggedIn(false);
      }
    } catch (error) {
      console.error("Error checking login status:", error);
      setJustLoggedIn(false);
    } finally {
      setIsCheckingLoginStatus(false);
    }
  };

  // Check login status on mount
  useEffect(() => {
    checkLoginStatus();
  }, []);
  // Check login status only when credentials become null (logout)
  useEffect(() => {
    if (!storedCredentails) {
      checkLoginStatus();
    }
  }, [storedCredentails]);

  // If still checking login status, show a blank screen
  if (isCheckingLoginStatus) {
    return null;
  }

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator>
        {storedCredentails ? (
          <>
            {/* If user just logged in, show BottomTabNavigator first */}
            {/* @ts-ignore */}
            {/* {console.log(storedCredentails)} */}
            {/* storedCredentails?.user?.hasPin === false ? (
              <>
                <Stack.Screen
                  name="AccountVerificationPromt1"
                  component={AccountVerificationPromt1}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
              </>
            ) :  */}
            {justLoggedIn || storedCredentails?.user?.hasPin === false ? (
              <Stack.Screen
                name="BottomTabNavigator1"
                component={BottomTabNavigator}
                options={{
                  headerTransparent: true,
                  headerShadowVisible: false,
                  headerShown: false,
                }}
              />
            ) : (
              <Stack.Screen
                name="ExistingLoginScreen"
                component={ExistingLoginScreen}
                options={{
                  headerTransparent: true,
                  headerShadowVisible: false,
                  headerShown: false,
                }}
              />
            )}

            <Stack.Screen
              name="ExistingLoginScreenReturn"
              component={ExistingLoginScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />

            <Stack.Screen
              name="VerifyActivityScreen"
              component={VerifyActivityScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BottomTabNavigator"
              component={BottomTabNavigator}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SelectAccountScreen"
              component={SelectAccountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AccountDetailsEntry"
              component={AccountDetailsEntry}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AccountVerificationPromt"
              component={AccountVerificationPromt}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BillPaymentIndex"
              component={BillPaymentIndex}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="WaterIndex"
              component={WaterIndex}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="WaterAmountScreen"
              component={WaterAmountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="WaterTransactionDetails"
              component={WaterTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ProfileIndex"
              component={ProfileIndex}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ProfileAccountTier"
              component={ProfileAccountTier}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ProfileUpgradeAccount"
              component={ProfileUpgradeAccount}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SecurityIndex"
              component={SecurityIndex}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SecurtityChangePassword"
              component={SecurtityChangePassword}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SecurtityEmailVerification"
              component={SecurtityEmailVerification}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SecurtityChangeTransactionPin"
              component={SecurtityChangeTransactionPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SecurtityQuestion"
              component={SecurtityQuestion}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="FaceId"
              component={FaceId}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="NotificationIndex"
              component={NotificationIndex}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ReferralScreen"
              component={ReferralScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ReferralProgramScreen"
              component={ReferralProgramScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ReferralProgramListScreen"
              component={ReferralProgramListScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ViewMoreLeaderboard"
              component={ViewMoreLeaderboard}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ReferralContestScreen"
              component={ReferralContestScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ContestRuleScreen"
              component={ContestRulesScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="NewReferralRules"
              component={NewReferralRules}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="RulesScreen"
              component={RulesScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ElecricityIndex"
              component={ElecricityIndex}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ElectricityAmountScreen"
              component={ElectricityAmountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ElectricityTransactionDetails"
              component={ElectricityTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="InternetIndex"
              component={InternetIndex}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="InternetAmountScreen"
              component={InternetAmountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="InternetTransactionDetails"
              component={InternetTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AirtimeIndex"
              component={AirtimeIndex}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AirtimeAmountScreen"
              component={AirtimeAmountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AirtimeTransactionDetails"
              component={AirtimeTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AmountScreen"
              component={AmountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SfxConfirmDetailsScreen"
              component={SfxConfirmDetailsScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TransactionStatus"
              component={TransactionStatus}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SfxTransactionDetails"
              component={SfxTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SfxTransactionReciept"
              component={SfxTransactionReciept}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TransactionScreen"
              component={TransactionScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="VerifyIdentity"
              component={VerifyIdentity}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="FaceIdScreen"
              component={FaceIdScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AllSetScreen2"
              component={AllSetScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SendMoneyStatus"
              component={SendMoneyStatus}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SendMoneyScreen"
              component={SendMoneyScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="NotificationScreen"
              component={NotificationScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ConfirmDetailScreen"
              component={ConfirmDetailScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankTransferScreen"
              component={BankTransferScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MobileMoneyScreen"
              component={MobileMoneyScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MobileMoneyScreen1"
              component={MobileMoneyScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MobileMoneyScreen2"
              component={MobileMoneyScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankMobileMoneyScreen1"
              component={BankMobileMoneyScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankMobileMoneyScreen2"
              component={BankMobileMoneyScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AmountScreen1"
              component={AmountScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ConfirmDetailScreen1"
              component={ConfirmDetailScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TransactionDetails1"
              component={TransactionDetails1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TransactionReceipt"
              component={TransactionReceipt}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankAmountScreen1"
              component={BankAmountScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankConfirmDetailScreen1"
              component={BankConfirmDetailScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankTransactionDetails1"
              component={BankTransactionDetails1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankTransactionReceipt"
              component={BankTransactionReceipt}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="P2pScreen"
              component={P2pScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="P2pScreen2"
              component={P2pScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="P2pAmountScreen"
              component={P2pAmountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="P2pConfirmDetails"
              component={P2pConfirmDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="P2pTransactionPin"
              component={P2pTransactionPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankTransactionPin"
              component={BankTransactionPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MobileMoneyTransactionPin"
              component={MobileMoneyTransactionPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="P2pTransactionDetails"
              component={P2pTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="P2pTransactionReciept"
              component={P2pTransactionReceipt}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyScreen"
              component={AddMoneyScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyAccountSelectScreen"
              component={AddMoneyAccountSelectScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyAccountScreen"
              component={AddMoneyAccountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyTransactionDetailsSfxMoneyApp"
              component={AddMoneyTransactionDetailsSfxMoneyApp}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyBankAccountScreen"
              component={AddMoneyBankAccountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyAmountScreen1"
              component={AddMoneyAmountScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankTransactionDetails2"
              component={BankTransactionDetails2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MoneySentScreen1"
              component={MoneySentScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyMobileMoneyAccountSelect"
              component={AddMoneyMobileMoneyAccountSelect}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyAmountScreen2"
              component={AddMoneyAmountScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ConfirmDetailScreen2"
              component={ConfirmDetailScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MobileNoteScreen"
              component={MobileNoteScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MobileMoneyTransfar"
              component={MobileMoneyTransfar}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MobileMoneyTransactionDetails"
              component={MobileMoneyTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MoneySentScreen2"
              component={MoneySentScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyP2pScreen"
              component={AddMoneyP2pScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyP2pAccountScreen"
              component={AddMoneyP2pAccountScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyP2pTransactionDetails"
              component={AddMoneyP2pTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankNoticeScreen"
              component={BankNoticeScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BankNoticeScreen1"
              component={BankNoticeScreen1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardGetStartedScreen"
              component={CardGetStartedScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardApplicationScreen"
              component={CardApplicationScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardAddNameScreen"
              component={CardAddNameScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="PhysicalCardPromt"
              component={PhysicalCardPromt}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />

            <Stack.Screen
              name="AccountVerification1"
              component={AccountVerification1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AccountVerification2"
              component={AccountVerification2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AccountVerification3"
              component={AccountVerification3}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AccountVerification4"
              component={AccountVerification4}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="FaceIDScreen2"
              component={FaceIdScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AllSetScreen"
              component={AllSetScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="PersonalInfo"
              component={PersonalInfo}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardTypeScreen"
              component={CardTypeScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardFeeDetailsScreen"
              component={CardFeeDetailsScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardPayPinScreen"
              component={CardPayPinScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardPayStatusScreen"
              component={CardPayStatusScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardTopStatus"
              component={CardTopStatus}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardFeeReciept"
              component={CardFeeReciept}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardHistory"
              component={CardHistory}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardTransactionDetails"
              component={CardTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardReciept"
              component={CardReciept}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardScreen"
              component={CardScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="PinManagementScreen"
              component={PinManagementScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CreateCardPin"
              component={CreateCardPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="DeleteCardPin"
              component={DeleteCardPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ConfirmCardPin"
              component={ConfirmCardPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardFeeTransactionDetails"
              component={CardFeeTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardSendScreen"
              component={CardSendScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="EditSfxBene"
              component={EditSfxBene}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardSendDetails"
              component={CardSendDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardPin"
              component={CardPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardSendStatus"
              component={CardSendStatus}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardTopUp"
              component={CardTopUp}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardTopUpDetails"
              component={CardTopUpDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TransactionPinScreen2"
              component={TransactionPinScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardLimitScreen"
              component={CardLimitScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardManagementScreen"
              component={CardManagementScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="FreezeCardPin"
              component={FreezeCardPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ChangePin1"
              component={ChangePin1}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ChangePin2"
              component={ChangePin2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ChangePin3"
              component={ChangePin3}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="DeleteCardPromt"
              component={DeleteCardPromt}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="DeleteCardSelect"
              component={DeleteCardSelect}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SwapScreen"
              component={SwapScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SwapScreenPromt"
              component={SwapScreenPromt}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyRate"
              component={AddMoneyRate}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="WalletViewScreen"
              component={WalletViewScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="WalletDetails"
              component={WalletDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="RewardScreen"
              component={RewardScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="PointDetailsScreen"
              component={PointDetailsScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="PointTransactionDetails"
              component={PointTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="Rules"
              component={Rules}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AllTransactionDetails"
              component={AllTransactionsDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AllRTransactionDetails"
              component={AllRTransactionsDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ConfirmNewPin"
              component={ConfirmNewPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ReferralListScreen"
              component={ReferralListScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AboutUs"
              component={AboutUs}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AllRecieptScreen"
              component={AllRecieptScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AllRRecieptScreen"
              component={AllRRecieptScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="MainRewardScreen"
              component={MainRewardScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="CardColor"
              component={CardColor}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="OnlineMarchent"
              component={OnlineMarchent}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SignupSuccessScreen"
              component={SignupSuccessScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="Beneficiaries"
              component={Beneficiaries}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="Rate2"
              component={Rate2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyRate2"
              component={AddMoneyRate2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="Discover2"
              component={Discover2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ExistingFp"
              component={ExistingFp}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ExistingRp"
              component={ExistingRp}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ResetSuccessScreen2"
              component={ResetSuccessScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ConfirmTransactionPin"
              component={ConfirmTransactionPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkAddAmount"
              component={LinkAddAmount}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkAddAccountSelect"
              component={LinkAddAccountSelect}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkAddConfirmDetail"
              component={LinkAddConfirmDetail}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkTransferScreen"
              component={LinkTransferScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkSentMoney"
              component={LinkSentMoney}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkTransactionDetails"
              component={LinkTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkReciept"
              component={LinkReciept}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkSendAmount"
              component={LinkSendAmount}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkSendConfirmDetails"
              component={LinkSendConfirmDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkTransactionPin"
              component={LinkTransactionPin}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkSendStatus"
              component={LinkSendStatus}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkSendTransactionDetails"
              component={LinkSendTransactionDetails}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="LinkSendReciept"
              component={LinkSendReciept}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AccountVerificationPending"
              component={AccountVerificationPending}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BillingAddressScreen"
              component={BillingAddressScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AllGuideScreen"
              component={AllGuideScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TwofactorAuthScreen1"
              component={TwofacttoAuthScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TwofactorAuthScreen2"
              component={TwofacttoAuthScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TwofactorBackupCodeScreen"
              component={TwofactorBackupCodeScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="OTCDeckSelectScreen"
              component={OTCDeckSelectScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="OTCTransactionScreen"
              component={OTCTransactionScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="OTCAccountDetailScreen"
              component={OTCAccountDetailScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="OTCConfirmTransactionScreen"
              component={OTCConfirmTransactionScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="OTCPinScreen"
              component={OTCPinScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="OTCTransactionStatus"
              component={OTCTransactionStatus}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="InAppBrowserScreen"
              component={InAppBrowserScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SumsubKYCScreen"
              component={SumSubKYCScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
          </>
        ) : (
          <>
            <Stack.Screen
              name="OnboardingScreen"
              component={OnboardingScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="NewLoginScreen"
              component={NewLoginScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ForgotPasswordScreen"
              component={ForgotPasswordScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SignupScreen"
              component={SignupScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="VerifyEmailScreen2"
              component={VerifyEmailScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ConfirmInformationScreen"
              component={ConfirmInformationScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="VerifyEmailScreen"
              component={VerifyEmailScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ResetPasswordScreen"
              component={ResetPasswordScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="ResetSuccessScreen"
              component={ResetSuccessScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />

            <Stack.Screen
              name="CreatenameScreen"
              component={CreatenameScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TransactionPinScreen"
              component={TransactionPinScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="SignupSuccessScreen"
              component={SignupSuccessScreen}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="Rate2"
              component={Rate2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="AddMoneyRate2"
              component={AddMoneyRate2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="Discover2"
              component={Discover2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="BetaTest"
              component={BetaTest}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="VerifySessionScreen"
              component={VerifySession}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="TwofactorAuthScreen2"
              component={TwofacttoAuthScreen2}
              options={{
                headerTransparent: true,
                headerShadowVisible: false,
                headerShown: false,
              }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

// Export the wrapper component
export default function MainStack() {
  return <MainStackWithCredentials />;
}
